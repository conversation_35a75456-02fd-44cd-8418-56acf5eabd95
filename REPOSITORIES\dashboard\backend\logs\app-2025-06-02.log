{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mWebSocket server started on port 5001\u001b[39m","timestamp":"2025-06-02 13:51:55:5155"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m<PERSON><PERSON><PERSON> connected successfully\u001b[39m","timestamp":"2025-06-02 13:52:00:520"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-06-02 13:52:00:520"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTimescaleDB connected successfully\u001b[39m","timestamp":"2025-06-02 13:52:00:520"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to Elasticsearch: connect ECONNREFUSED 172.18.0.5:9200\u001b[39m","meta":{"headers":{},"meta":{"aborted":false,"attempts":3,"connection":{"_openRequests":0,"deadCount":0,"headers":{"user-agent":"elasticsearch-js/8.18.2 (linux 5.15.167.4-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"id":"http://elasticsearch:9200/","maxEventListeners":100,"pool":{"_events":{},"_eventsCount":1},"resurrectTimeout":0,"timeout":30000,"tls":null,"url":"http://elasticsearch:9200/","weight":1000},"context":null,"name":"elasticsearch-js","request":{"id":1,"options":{},"params":{"headers":{"accept":"application/vnd.elasticsearch+json; compatible-with=8,text/plain","user-agent":"elasticsearch-js/8.18.2 (linux 5.15.167.4-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"method":"HEAD","path":"/","querystring":""}}},"statusCode":0,"warnings":null},"name":"ConnectionError","options":{"redaction":{"additionalKeys":[],"type":"replace"}},"stack":"ConnectionError: connect ECONNREFUSED 172.18.0.5:9200\n    at SniffingTransport._request (/app/node_modules/@elastic/transport/lib/Transport.js:595:31)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/node_modules/@elastic/transport/lib/Transport.js:627:32\n    at async SniffingTransport.request (/app/node_modules/@elastic/transport/lib/Transport.js:623:20)\n    at async Client.PingApi [as ping] (/app/node_modules/@elastic/elasticsearch/lib/api/api/ping.js:41:12)\n    at async DatabaseManager.connectElasticsearch (/app/src/config/database.js:73:7)\n    at async Promise.allSettled (index 2)\n    at async DatabaseManager.connectAll (/app/src/config/database.js:109:21)\n    at async ExLogServer.connectDatabases (/app/src/index.js:135:7)\n    at async ExLogServer.start (/app/src/index.js:146:7)","timestamp":"2025-06-02 13:52:08:528"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m3 databases connected successfully\u001b[39m","timestamp":"2025-06-02 13:52:08:528"}
{"0":"connect ECONNREFUSED 172.18.0.5:9200","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m1 databases failed to connect:\u001b[39m","timestamp":"2025-06-02 13:52:08:528"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections established\u001b[39m","timestamp":"2025-06-02 13:52:08:528"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI documentation enabled at /api/docs\u001b[39m","timestamp":"2025-06-02 13:52:08:528"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExLog API server running on port 5000\u001b[39m","timestamp":"2025-06-02 13:52:08:528"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-06-02 13:52:08:528"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-02 13:54:01:541"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 3 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 13:57:54:5754"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 3 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:06:17:617"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:13:47:1347"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:13:47:1347","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:18:41:1841"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:18:41:1841","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:19:02:192"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 3 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:19:07:197"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:20:46:2046"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:20:46:2046","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:25:48:2548"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:25:49:2549","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:30:15:3015"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:30:15:3015"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:30:15:3015"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:30:15:3015"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:30:15:3015"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:30:15:3015"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:30:15:3015"}
{"code":11000,"errorLabelSet":{},"errorResponse":{"code":11000,"errmsg":"E11000 duplicate key error collection: exlog.logs index: logId_1 dup key: { logId: \"94feaade-5de3-44b7-958a-13ea30ad8c41\" }","index":0,"keyPattern":{"logId":1},"keyValue":{"logId":"94feaade-5de3-44b7-958a-13ea30ad8c41"}},"index":0,"keyPattern":{"logId":1},"keyValue":{"logId":"94feaade-5de3-44b7-958a-13ea30ad8c41"},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to process log 94feaade-5de3-44b7-958a-13ea30ad8c41: E11000 duplicate key error collection: exlog.logs index: logId_1 dup key: { logId: \"94feaade-5de3-44b7-958a-13ea30ad8c41\" }\u001b[39m","stack":"MongoServerError: E11000 duplicate key error collection: exlog.logs index: logId_1 dup key: { logId: \"94feaade-5de3-44b7-958a-13ea30ad8c41\" }\n    at InsertOneOperation.execute (/app/node_modules/mongodb/lib/operations/insert.js:51:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async tryOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:207:20)\n    at async executeOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:75:16)\n    at async Collection.insertOne (/app/node_modules/mongodb/lib/collection.js:157:16)","timestamp":"2025-06-02 14:30:15:3015"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 0 logs, failed 1 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:30:15:3015"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:35:05:355"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:35:06:356","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:36:49:3649"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:36:49:3649"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:36:49:3649"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:36:49:3649"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:36:49:3649"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:36:49:3649"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:36:49:3649"}
{"code":11000,"errorLabelSet":{},"errorResponse":{"code":11000,"errmsg":"E11000 duplicate key error collection: exlog.logs index: logId_1 dup key: { logId: \"94feaade-5de3-44b7-958a-13ea30ad8c41\" }","index":0,"keyPattern":{"logId":1},"keyValue":{"logId":"94feaade-5de3-44b7-958a-13ea30ad8c41"}},"index":0,"keyPattern":{"logId":1},"keyValue":{"logId":"94feaade-5de3-44b7-958a-13ea30ad8c41"},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to process log 94feaade-5de3-44b7-958a-13ea30ad8c41: E11000 duplicate key error collection: exlog.logs index: logId_1 dup key: { logId: \"94feaade-5de3-44b7-958a-13ea30ad8c41\" }\u001b[39m","stack":"MongoServerError: E11000 duplicate key error collection: exlog.logs index: logId_1 dup key: { logId: \"94feaade-5de3-44b7-958a-13ea30ad8c41\" }\n    at InsertOneOperation.execute (/app/node_modules/mongodb/lib/operations/insert.js:51:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async tryOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:207:20)\n    at async executeOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:75:16)\n    at async Collection.insertOne (/app/node_modules/mongodb/lib/collection.js:157:16)","timestamp":"2025-06-02 14:36:49:3649"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 0 logs, failed 1 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:36:49:3649"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:37:09:379"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:37:09:379"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:37:09:379"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:37:09:379"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:37:09:379"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:37:09:379"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:37:09:379"}
{"code":11000,"errorLabelSet":{},"errorResponse":{"code":11000,"errmsg":"E11000 duplicate key error collection: exlog.logs index: logId_1 dup key: { logId: \"94feaade-5de3-44b7-958a-13ea30ad8c41\" }","index":0,"keyPattern":{"logId":1},"keyValue":{"logId":"94feaade-5de3-44b7-958a-13ea30ad8c41"}},"index":0,"keyPattern":{"logId":1},"keyValue":{"logId":"94feaade-5de3-44b7-958a-13ea30ad8c41"},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to process log 94feaade-5de3-44b7-958a-13ea30ad8c41: E11000 duplicate key error collection: exlog.logs index: logId_1 dup key: { logId: \"94feaade-5de3-44b7-958a-13ea30ad8c41\" }\u001b[39m","stack":"MongoServerError: E11000 duplicate key error collection: exlog.logs index: logId_1 dup key: { logId: \"94feaade-5de3-44b7-958a-13ea30ad8c41\" }\n    at InsertOneOperation.execute (/app/node_modules/mongodb/lib/operations/insert.js:51:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async tryOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:207:20)\n    at async executeOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:75:16)\n    at async Collection.insertOne (/app/node_modules/mongodb/lib/collection.js:157:16)","timestamp":"2025-06-02 14:37:09:379"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 0 logs, failed 1 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:37:09:379"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:38:20:3820"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:38:21:3821","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-02 14:42:56:4256"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:45:47:4547"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:45:47:4547","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:45:51:4551"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:45:51:4551"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:45:51:4551"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:45:51:4551"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:45:51:4551","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:45:56:4556","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:45:56:4556"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:45:56:4556"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:45:56:4556"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:45:56:4556"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:45:56:4556","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:46:03:463","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:46:03:463"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:46:03:463"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:46:03:463"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:46:03:463"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:46:03:463","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:46:07:467","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:46:07:467"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:46:07:467"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:46:07:467"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:46:07:467"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:46:07:467","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:46:07:467","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:54:20:5420"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:54:20:5420","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:54:25:5425"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:54:25:5425"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:54:25:5425"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:54:25:5425","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:54:25:5425"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:54:31:5431","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:54:32:5432"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:54:32:5432"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:54:32:5432","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:54:32:5432","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:54:32:5432"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:54:32:5432","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-02 14:56:37:5637"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:57:04:574","url":"/api/v1/logs?page=1&limit=50&search=&source=&logLevel=critical&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:57:11:5711","url":"/api/v1/logs?page=1&limit=50&search=&source=System&logLevel=&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:57:16:5716","url":"/api/v1/logs?page=1&limit=50&search=system&source=System&logLevel=&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-02 14:57:56:5756"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-02 14:58:29:5829"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:58:49:5849"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:58:49:5849","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:58:53:5853"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:58:53:5853"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:58:53:5853"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:58:54:5854"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:58:54:5854"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:58:58:5858"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:58:58:5858"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:58:58:5858"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:58:58:5858"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:58:58:5858"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:58:58:5858"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:58:58:5858"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:59:03:593"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:59:03:593"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:59:03:593"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:59:03:593"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:59:03:593"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:59:03:593"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 14:59:03:593"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-02 14:59:11:5911"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:59:29:5929","url":"/api/v1/logs?page=2&limit=50&source=&logLevel=&host=&search=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:59:33:5933","url":"/api/v1/logs?page=1&limit=50&search=&source=&logLevel=critical&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:59:43:5943","url":"/api/v1/logs?page=1&limit=50&search=&source=System&logLevel=&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:59:51:5951","url":"/api/v1/logs?page=1&limit=50&search=fail&source=&logLevel=&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:59:56:5956","url":"/api/v1/logs?page=1&limit=50&source=&logLevel=&host=&search=fail","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:00:05:05","url":"/api/v1/logs?page=1&limit=50&source=&logLevel=&host=&search=fail","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:00:14:014","url":"/api/v1/logs?page=2&limit=50&source=&logLevel=&host=&search=fail","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:00:31:031"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:00:31:031","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:00:36:036"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:00:36:036"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:00:36:036"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:00:36:036"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:00:36:036"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:00:41:041"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:00:41:041"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:00:41:041"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:00:41:041"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:00:41:041"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:00:41:041"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:00:41:041"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:00:44:044","url":"/api/v1/logs?page=1&limit=50&source=&logLevel=&host=&search=fail","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:00:45:045"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:00:47:047","url":"/api/v1/logs?page=2&limit=50&source=&logLevel=&host=&search=fail","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:00:48:048","url":"/api/v1/logs?page=2&limit=50&source=&logLevel=&host=&search=fail","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:00:52:052","url":"/api/v1/logs?page=2&limit=50&source=&logLevel=&host=&search=fail","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:00:52:052"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:00:52:052"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:00:52:052"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:00:52:052"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:00:52:052"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:00:52:052"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:00:52:052"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:00:52:052"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:00:52:052"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:00:52:052"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:00:52:052"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:00:53:053"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:00:53:053"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:00:53:053"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:00:57:057"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:00:57:057"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:00:57:057"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:00:57:057"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:00:57:057"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:00:57:057"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:01:01:11"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:01:01:11"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:01:01:11"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:01:01:11"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:01:01:11"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:01:01:11"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:01:02:12"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:01:07:17","url":"/api/v1/logs?page=1&limit=50&source=&logLevel=&host=&search=fail","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-02 15:01:11:111"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:01:15:115","url":"/api/v1/logs?page=2&limit=50&source=&logLevel=&host=&search=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-02 15:05:14:514"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-02 15:06:11:611"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-02 15:06:53:653"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:07:02:72","url":"/api/v1/logs?page=1&limit=50&search=&source=&logLevel=critical&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:07:08:78","url":"/api/v1/logs?page=1&limit=50&search=&source=System&logLevel=&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:08:18:818","url":"/api/v1/logs?page=1&limit=50&search=&source=&logLevel=info&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:08:19:819","url":"/api/v1/logs?page=1&limit=50&search=&source=&logLevel=info&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:08:19:819","url":"/api/v1/logs?page=1&limit=50&search=&source=&logLevel=info&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:08:19:819","url":"/api/v1/logs?page=1&limit=50&search=&source=&logLevel=info&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:08:19:819","url":"/api/v1/logs?page=1&limit=50&search=&source=&logLevel=info&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:08:19:819","url":"/api/v1/logs?page=1&limit=50&search=&source=&logLevel=info&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:08:20:820","url":"/api/v1/logs?page=1&limit=50&search=&source=&logLevel=info&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:08:20:820","url":"/api/v1/logs?page=1&limit=50&search=&source=&logLevel=info&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:08:20:820","url":"/api/v1/logs?page=1&limit=50&search=&source=&logLevel=info&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:08:20:820","url":"/api/v1/logs?page=1&limit=50&search=&source=&logLevel=info&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM, shutting down WebSocket server...\u001b[39m","timestamp":"2025-06-02 15:09:56:956"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM. Starting graceful shutdown...\u001b[39m","timestamp":"2025-06-02 15:09:57:957"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP server closed\u001b[39m","timestamp":"2025-06-02 15:09:57:957"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mMongoDB disconnected\u001b[39m","timestamp":"2025-06-02 15:09:57:957"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections closed\u001b[39m","timestamp":"2025-06-02 15:09:57:957"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mWebSocket server started on port 5001\u001b[39m","timestamp":"2025-06-02 15:18:05:185"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connected successfully\u001b[39m","timestamp":"2025-06-02 15:18:11:1811"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-06-02 15:18:11:1811"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTimescaleDB connected successfully\u001b[39m","timestamp":"2025-06-02 15:18:12:1812"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to Elasticsearch: connect ECONNREFUSED 172.18.0.5:9200\u001b[39m","meta":{"headers":{},"meta":{"aborted":false,"attempts":3,"connection":{"_openRequests":0,"deadCount":0,"headers":{"user-agent":"elasticsearch-js/8.18.2 (linux 5.15.167.4-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"id":"http://elasticsearch:9200/","maxEventListeners":100,"pool":{"_events":{},"_eventsCount":1},"resurrectTimeout":0,"timeout":30000,"tls":null,"url":"http://elasticsearch:9200/","weight":1000},"context":null,"name":"elasticsearch-js","request":{"id":1,"options":{},"params":{"headers":{"accept":"application/vnd.elasticsearch+json; compatible-with=8,text/plain","user-agent":"elasticsearch-js/8.18.2 (linux 5.15.167.4-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"method":"HEAD","path":"/","querystring":""}}},"statusCode":0,"warnings":null},"name":"ConnectionError","options":{"redaction":{"additionalKeys":[],"type":"replace"}},"stack":"ConnectionError: connect ECONNREFUSED 172.18.0.5:9200\n    at SniffingTransport._request (/app/node_modules/@elastic/transport/lib/Transport.js:595:31)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/node_modules/@elastic/transport/lib/Transport.js:627:32\n    at async SniffingTransport.request (/app/node_modules/@elastic/transport/lib/Transport.js:623:20)\n    at async Client.PingApi [as ping] (/app/node_modules/@elastic/elasticsearch/lib/api/api/ping.js:41:12)\n    at async DatabaseManager.connectElasticsearch (/app/src/config/database.js:73:7)\n    at async Promise.allSettled (index 2)\n    at async DatabaseManager.connectAll (/app/src/config/database.js:109:21)\n    at async ExLogServer.connectDatabases (/app/src/index.js:135:7)\n    at async ExLogServer.start (/app/src/index.js:146:7)","timestamp":"2025-06-02 15:18:22:1822"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m3 databases connected successfully\u001b[39m","timestamp":"2025-06-02 15:18:22:1822"}
{"0":"connect ECONNREFUSED 172.18.0.5:9200","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m1 databases failed to connect:\u001b[39m","timestamp":"2025-06-02 15:18:22:1822"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections established\u001b[39m","timestamp":"2025-06-02 15:18:22:1822"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI documentation enabled at /api/docs\u001b[39m","timestamp":"2025-06-02 15:18:22:1822"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExLog API server running on port 5000\u001b[39m","timestamp":"2025-06-02 15:18:22:1822"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-06-02 15:18:22:1822"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-02 15:18:27:1827"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:20:44:2044"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:20:44:2044","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:20:49:2049"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:20:49:2049"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:20:49:2049"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:20:49:2049"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:20:49:2049"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:20:54:2054"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:20:54:2054"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:20:54:2054"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:20:54:2054"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:20:54:2054"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:20:54:2054"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:20:54:2054"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:20:56:2056"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:20:56:2056"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:20:56:2056"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:20:56:2056"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:20:56:2056"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:20:56:2056"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:20:57:2057"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:23:14:2314"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:23:14:2314","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:23:19:2319"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:23:19:2319"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:23:19:2319"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:23:19:2319"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:23:19:2319"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:23:19:2319"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:23:24:2324"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:23:24:2324"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:23:24:2324"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:23:24:2324"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:23:24:2324"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:23:24:2324"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM. Starting graceful shutdown...\u001b[39m","timestamp":"2025-06-02 15:27:52:2752"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP server closed\u001b[39m","timestamp":"2025-06-02 15:27:52:2752"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mMongoDB disconnected\u001b[39m","timestamp":"2025-06-02 15:27:52:2752"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections closed\u001b[39m","timestamp":"2025-06-02 15:27:52:2752"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connected successfully\u001b[39m","timestamp":"2025-06-02 15:27:57:2757"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-06-02 15:27:57:2757"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTimescaleDB connected successfully\u001b[39m","timestamp":"2025-06-02 15:27:57:2757"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mElasticsearch connected successfully\u001b[39m","timestamp":"2025-06-02 15:27:57:2757"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m4 databases connected successfully\u001b[39m","timestamp":"2025-06-02 15:27:57:2757"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections established\u001b[39m","timestamp":"2025-06-02 15:27:57:2757"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI documentation enabled at /api/docs\u001b[39m","timestamp":"2025-06-02 15:27:57:2757"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExLog API server running on port 5000\u001b[39m","timestamp":"2025-06-02 15:27:57:2757"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-06-02 15:27:57:2757"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-02 15:29:51:2951"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 2 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:29:51:2951"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-02 15:33:27:3327"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-02 15:33:36:3336"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-02 15:33:47:3347"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:34:15:3415"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:34:15:3415","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:34:22:3422"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:34:22:3422"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:34:22:3422"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:34:22:3422"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:34:22:3422"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:34:26:3426"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:34:26:3426"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:34:27:3427"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:34:27:3427"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:34:27:3427"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:34:27:3427"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683a1b564430c4061ad861e0\u001b[39m","timestamp":"2025-06-02 15:34:27:3427"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM, shutting down WebSocket server...\u001b[39m","timestamp":"2025-06-02 15:36:38:3638"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mWebSocket server started on port 5001\u001b[39m","timestamp":"2025-06-02 15:36:43:3643"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connected successfully\u001b[39m","timestamp":"2025-06-02 15:36:47:3647"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-06-02 15:36:47:3647"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTimescaleDB connected successfully\u001b[39m","timestamp":"2025-06-02 15:36:47:3647"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to Elasticsearch: connect ECONNREFUSED 172.18.0.5:9200\u001b[39m","meta":{"headers":{},"meta":{"aborted":false,"attempts":3,"connection":{"_openRequests":0,"deadCount":0,"headers":{"user-agent":"elasticsearch-js/8.18.2 (linux 5.15.167.4-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"id":"http://elasticsearch:9200/","maxEventListeners":100,"pool":{"_events":{},"_eventsCount":1},"resurrectTimeout":0,"timeout":30000,"tls":null,"url":"http://elasticsearch:9200/","weight":1000},"context":null,"name":"elasticsearch-js","request":{"id":1,"options":{},"params":{"headers":{"accept":"application/vnd.elasticsearch+json; compatible-with=8,text/plain","user-agent":"elasticsearch-js/8.18.2 (linux 5.15.167.4-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"method":"HEAD","path":"/","querystring":""}}},"statusCode":0,"warnings":null},"name":"ConnectionError","options":{"redaction":{"additionalKeys":[],"type":"replace"}},"stack":"ConnectionError: connect ECONNREFUSED 172.18.0.5:9200\n    at SniffingTransport._request (/app/node_modules/@elastic/transport/lib/Transport.js:595:31)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/node_modules/@elastic/transport/lib/Transport.js:627:32\n    at async SniffingTransport.request (/app/node_modules/@elastic/transport/lib/Transport.js:623:20)\n    at async Client.PingApi [as ping] (/app/node_modules/@elastic/elasticsearch/lib/api/api/ping.js:41:12)\n    at async DatabaseManager.connectElasticsearch (/app/src/config/database.js:73:7)\n    at async Promise.allSettled (index 2)\n    at async DatabaseManager.connectAll (/app/src/config/database.js:109:21)\n    at async ExLogServer.connectDatabases (/app/src/index.js:135:7)\n    at async ExLogServer.start (/app/src/index.js:146:7)","timestamp":"2025-06-02 15:36:54:3654"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m3 databases connected successfully\u001b[39m","timestamp":"2025-06-02 15:36:54:3654"}
{"0":"connect ECONNREFUSED 172.18.0.5:9200","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m1 databases failed to connect:\u001b[39m","timestamp":"2025-06-02 15:36:54:3654"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections established\u001b[39m","timestamp":"2025-06-02 15:36:54:3654"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI documentation enabled at /api/docs\u001b[39m","timestamp":"2025-06-02 15:36:55:3655"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExLog API server running on port 5000\u001b[39m","timestamp":"2025-06-02 15:36:55:3655"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-06-02 15:36:55:3655"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-02 15:37:19:3719"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-02 15:39:11:3911"}
