```mermaid
graph TB
    %% External Systems and Users
    subgraph "External Environment"
        Users[👥 Security Analysts<br/>& Administrators]
        LinuxSystems[🐧 Linux Servers<br/>& Workstations]
        WindowsSystems[🪟 Windows Servers<br/>& Workstations]
        NetworkDevices[🌐 Network Devices<br/>Firewalls, Routers]
    end

    %% Dashboard Platform (Main System)
    subgraph "ExLog Dashboard Platform"
        subgraph "Frontend Layer"
            ReactApp[⚛️ React Frontend<br/>- Material-UI Components<br/>- Real-time Dashboard<br/>- Log Search & Analytics<br/>- Alert Management<br/>- Agent Management]
            Nginx[🔧 Nginx Reverse Proxy<br/>- Load Balancing<br/>- SSL Termination<br/>- Static File Serving<br/>- Request Routing]
        end

        subgraph "API Layer"
            ExpressAPI[🚀 Express.js Backend<br/>- RESTful API<br/>- Authentication<br/>- Authorization<br/>- Request Validation]
            WebSocketServer[🔌 WebSocket Service<br/>- Real-time Updates<br/>- Live Alerts<br/>- Dashboard Refresh<br/>- Agent Status]
        end

        subgraph "Core Services"
            AuthService[🔐 Authentication Service<br/>- JWT Token Management<br/>- User Sessions<br/>- API Key Validation<br/>- Role-based Access]
            
            LogIngestionAPI[📥 Log Ingestion API<br/>- Agent Log Reception<br/>- Data Validation<br/>- Batch Processing<br/>- Rate Limiting]
            
            AlertEngine[🚨 Alert Correlation Engine<br/>- Rule Processing<br/>- Pattern Matching<br/>- Alert Generation<br/>- Notification Dispatch]
            
            QueryEngine[🔍 Query Engine<br/>- Log Search<br/>- Filtering & Aggregation<br/>- Analytics<br/>- Report Generation]
        end

        subgraph "Data Layer"
            MongoDB[(🍃 MongoDB Database<br/>- Logs Collection<br/>- Users & Permissions<br/>- Alert Rules<br/>- Agent Configurations<br/>- System Metadata)]
        end
    end

    %% Linux Agent System
    subgraph "Linux Agent (Python)"
        subgraph "Linux Collection Layer"
            SyslogCollector[📋 Syslog Collector<br/>- /var/log/syslog<br/>- /var/log/messages<br/>- Real-time Monitoring]
            AuthCollector[🔒 Auth Log Collector<br/>- /var/log/auth.log<br/>- /var/log/secure<br/>- SSH/Sudo Events]
            JournalCollector[📖 Systemd Journal<br/>- journalctl Integration<br/>- Service Logs<br/>- System Events]
            AppCollector[📱 Application Logs<br/>- Nginx/Apache Logs<br/>- Database Logs<br/>- Custom Applications]
            NetworkCollector[🌐 Network Logs<br/>- Interface Changes<br/>- Connection Events<br/>- Traffic Monitoring]
        end

        subgraph "Linux Processing Layer"
            LinuxStandardizer[⚙️ Log Standardizer<br/>- JSON Conversion<br/>- Field Normalization<br/>- Schema Validation<br/>- Error Handling]
            LinuxBuffer[💾 Timed Buffer<br/>- Batch Management<br/>- Offline Storage<br/>- Retry Logic<br/>- Rate Control]
            LinuxAPIClient[🔗 API Client<br/>- HTTP/HTTPS Transport<br/>- Authentication<br/>- Compression<br/>- Health Monitoring]
        end

        subgraph "Linux Service Layer"
            SystemdService[🔧 Systemd Service<br/>- Auto-start<br/>- Process Monitoring<br/>- Configuration Reload<br/>- Health Checks]
            LinuxConfigManager[⚙️ Configuration Manager<br/>- YAML Configuration<br/>- Dynamic Updates<br/>- Validation<br/>- Defaults]
        end
    end

    %% Windows Agent System
    subgraph "Windows Agent (Python)"
        subgraph "Windows Collection Layer"
            EventLogCollector[📋 Event Log Collector<br/>- System Events<br/>- Application Events<br/>- Security Events<br/>- Setup Events]
            SecurityCollector[🔒 Security Collector<br/>- Authentication Events<br/>- Policy Changes<br/>- Privilege Use<br/>- Account Management]
            AppLogCollector[📱 Application Collector<br/>- Service Events<br/>- Error Logs<br/>- Performance Logs<br/>- Custom Apps]
            SystemCollector[🖥️ System Collector<br/>- Hardware Events<br/>- Driver Failures<br/>- Boot Events<br/>- Service Status]
            NetworkCollector2[🌐 Network Collector<br/>- Connection Events<br/>- Interface Changes<br/>- Packet Capture<br/>- Traffic Analysis]
        end

        subgraph "Windows Processing Layer"
            WindowsStandardizer[⚙️ Log Standardizer<br/>- JSON Conversion<br/>- Field Mapping<br/>- Schema Compliance<br/>- Error Recovery]
            WindowsBuffer[💾 Buffer Manager<br/>- Memory Buffering<br/>- Disk Overflow<br/>- Batch Optimization<br/>- Retry Mechanism]
            WindowsAPIClient[🔗 API Client<br/>- Secure Transport<br/>- Authentication<br/>- Compression<br/>- Monitoring]
        end

        subgraph "Windows Service Layer"
            WindowsService[🔧 Windows Service<br/>- Service Manager<br/>- Auto-recovery<br/>- Event Logging<br/>- Performance Monitoring]
            WindowsConfigManager[⚙️ Configuration Manager<br/>- Registry Integration<br/>- File-based Config<br/>- Hot Reload<br/>- Validation]
        end
    end

    %% Data Flow Connections
    %% User Interactions
    Users -->|HTTPS Requests| Nginx
    Nginx -->|Static Content| ReactApp
    Nginx -->|API Requests| ExpressAPI
    Nginx -->|WebSocket| WebSocketServer

    %% Frontend to Backend
    ReactApp -->|API Calls| ExpressAPI
    ReactApp -->|Real-time Connection| WebSocketServer

    %% API Internal Flow
    ExpressAPI -->|Authentication| AuthService
    ExpressAPI -->|Log Queries| QueryEngine
    ExpressAPI -->|Agent Management| LogIngestionAPI
    
    %% Log Ingestion Flow
    LogIngestionAPI -->|Store Logs| MongoDB
    LogIngestionAPI -->|Trigger Analysis| AlertEngine
    AlertEngine -->|Store Alerts| MongoDB
    AlertEngine -->|Real-time Notifications| WebSocketServer
    
    %% Query Flow
    QueryEngine -->|Retrieve Data| MongoDB
    WebSocketServer -->|Live Data| MongoDB

    %% Linux Agent Data Flow
    LinuxSystems -->|Raw Logs| SyslogCollector
    LinuxSystems -->|Auth Events| AuthCollector
    LinuxSystems -->|Journal Logs| JournalCollector
    LinuxSystems -->|App Logs| AppCollector
    LinuxSystems -->|Network Events| NetworkCollector

    SyslogCollector -->|Parsed Data| LinuxStandardizer
    AuthCollector -->|Parsed Data| LinuxStandardizer
    JournalCollector -->|Parsed Data| LinuxStandardizer
    AppCollector -->|Parsed Data| LinuxStandardizer
    NetworkCollector -->|Parsed Data| LinuxStandardizer

    LinuxStandardizer -->|Standardized Logs| LinuxBuffer
    LinuxBuffer -->|Batched Logs| LinuxAPIClient
    LinuxAPIClient -->|HTTPS/JSON| LogIngestionAPI

    SystemdService -->|Manages| LinuxStandardizer
    LinuxConfigManager -->|Configures| SyslogCollector
    LinuxConfigManager -->|Configures| AuthCollector
    LinuxConfigManager -->|Configures| JournalCollector

    %% Windows Agent Data Flow
    WindowsSystems -->|Event Logs| EventLogCollector
    WindowsSystems -->|Security Events| SecurityCollector
    WindowsSystems -->|App Events| AppLogCollector
    WindowsSystems -->|System Events| SystemCollector
    WindowsSystems -->|Network Events| NetworkCollector2

    EventLogCollector -->|Parsed Data| WindowsStandardizer
    SecurityCollector -->|Parsed Data| WindowsStandardizer
    AppLogCollector -->|Parsed Data| WindowsStandardizer
    SystemCollector -->|Parsed Data| WindowsStandardizer
    NetworkCollector2 -->|Parsed Data| WindowsStandardizer

    WindowsStandardizer -->|Standardized Logs| WindowsBuffer
    WindowsBuffer -->|Batched Logs| WindowsAPIClient
    WindowsAPIClient -->|HTTPS/JSON| LogIngestionAPI

    WindowsService -->|Manages| WindowsStandardizer
    WindowsConfigManager -->|Configures| EventLogCollector
    WindowsConfigManager -->|Configures| SecurityCollector
    WindowsConfigManager -->|Configures| AppLogCollector

    %% External Network Devices (Future)
    NetworkDevices -.->|Syslog/SNMP| LogIngestionAPI

    %% Styling
    classDef userClass fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef frontendClass fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef backendClass fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef dataClass fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef linuxClass fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px
    classDef windowsClass fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef serviceClass fill:#f1f8e9,stroke:#33691e,stroke-width:2px

    class Users userClass
    class ReactApp,Nginx frontendClass
    class ExpressAPI,WebSocketServer,AuthService,LogIngestionAPI,AlertEngine,QueryEngine backendClass
    class MongoDB dataClass
    class SyslogCollector,AuthCollector,JournalCollector,AppCollector,NetworkCollector,LinuxStandardizer,LinuxBuffer,LinuxAPIClient linuxClass
    class EventLogCollector,SecurityCollector,AppLogCollector,SystemCollector,NetworkCollector2,WindowsStandardizer,WindowsBuffer,WindowsAPIClient windowsClass
    class SystemdService,LinuxConfigManager,WindowsService,WindowsConfigManager serviceClass
```