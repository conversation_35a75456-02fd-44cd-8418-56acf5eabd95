{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mWebSocket server started on port 5001\u001b[39m","timestamp":"2025-05-29 02:06:38:638"}
{"address":"**********","code":"ECONNREFUSED","errno":-111,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to TimescaleDB: connect ECONNREFUSED **********:5432\u001b[39m","port":5432,"stack":"Error: connect ECONNREFUSED **********:5432\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)","syscall":"connect","timestamp":"2025-05-29 02:06:44:644"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM, shutting down WebSocket server...\u001b[39m","timestamp":"2025-05-29 02:07:45:745"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mWebSocket server started on port 5001\u001b[39m","timestamp":"2025-05-29 02:08:20:820"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connected successfully\u001b[39m","timestamp":"2025-05-29 02:08:24:824"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-05-29 02:08:24:824"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTimescaleDB connected successfully\u001b[39m","timestamp":"2025-05-29 02:08:25:825"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to Elasticsearch: connect ECONNREFUSED 172.18.0.5:9200\u001b[39m","meta":{"headers":{},"meta":{"aborted":false,"attempts":3,"connection":{"_openRequests":0,"deadCount":0,"headers":{"user-agent":"elasticsearch-js/8.18.2 (linux 5.15.167.4-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"id":"http://elasticsearch:9200/","maxEventListeners":100,"pool":{"_events":{},"_eventsCount":1},"resurrectTimeout":0,"timeout":30000,"tls":null,"url":"http://elasticsearch:9200/","weight":1000},"context":null,"name":"elasticsearch-js","request":{"id":1,"options":{},"params":{"headers":{"accept":"application/vnd.elasticsearch+json; compatible-with=8,text/plain","user-agent":"elasticsearch-js/8.18.2 (linux 5.15.167.4-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"method":"HEAD","path":"/","querystring":""}}},"statusCode":0,"warnings":null},"name":"ConnectionError","options":{"redaction":{"additionalKeys":[],"type":"replace"}},"stack":"ConnectionError: connect ECONNREFUSED 172.18.0.5:9200\n    at SniffingTransport._request (/app/node_modules/@elastic/transport/lib/Transport.js:595:31)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/node_modules/@elastic/transport/lib/Transport.js:627:32\n    at async SniffingTransport.request (/app/node_modules/@elastic/transport/lib/Transport.js:623:20)\n    at async Client.PingApi [as ping] (/app/node_modules/@elastic/elasticsearch/lib/api/api/ping.js:41:12)\n    at async DatabaseManager.connectElasticsearch (/app/src/config/database.js:73:7)\n    at async Promise.all (index 2)\n    at async DatabaseManager.connectAll (/app/src/config/database.js:110:7)\n    at async ExLogServer.connectDatabases (/app/src/index.js:90:7)\n    at async ExLogServer.start (/app/src/index.js:101:7)","timestamp":"2025-05-29 02:08:33:833"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM, shutting down WebSocket server...\u001b[39m","timestamp":"2025-05-29 02:09:14:914"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mWebSocket server started on port 5001\u001b[39m","timestamp":"2025-05-29 02:09:55:955"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connected successfully\u001b[39m","timestamp":"2025-05-29 02:09:59:959"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-05-29 02:09:59:959"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTimescaleDB connected successfully\u001b[39m","timestamp":"2025-05-29 02:09:59:959"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to Elasticsearch: connect ECONNREFUSED 172.18.0.5:9200\u001b[39m","meta":{"headers":{},"meta":{"aborted":false,"attempts":3,"connection":{"_openRequests":0,"deadCount":0,"headers":{"user-agent":"elasticsearch-js/8.18.2 (linux 5.15.167.4-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"id":"http://elasticsearch:9200/","maxEventListeners":100,"pool":{"_events":{},"_eventsCount":1},"resurrectTimeout":0,"timeout":30000,"tls":null,"url":"http://elasticsearch:9200/","weight":1000},"context":null,"name":"elasticsearch-js","request":{"id":1,"options":{},"params":{"headers":{"accept":"application/vnd.elasticsearch+json; compatible-with=8,text/plain","user-agent":"elasticsearch-js/8.18.2 (linux 5.15.167.4-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"method":"HEAD","path":"/","querystring":""}}},"statusCode":0,"warnings":null},"name":"ConnectionError","options":{"redaction":{"additionalKeys":[],"type":"replace"}},"stack":"ConnectionError: connect ECONNREFUSED 172.18.0.5:9200\n    at SniffingTransport._request (/app/node_modules/@elastic/transport/lib/Transport.js:595:31)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/node_modules/@elastic/transport/lib/Transport.js:627:32\n    at async SniffingTransport.request (/app/node_modules/@elastic/transport/lib/Transport.js:623:20)\n    at async Client.PingApi [as ping] (/app/node_modules/@elastic/elasticsearch/lib/api/api/ping.js:41:12)\n    at async DatabaseManager.connectElasticsearch (/app/src/config/database.js:73:7)\n    at async Promise.all (index 2)\n    at async DatabaseManager.connectAll (/app/src/config/database.js:110:7)\n    at async ExLogServer.connectDatabases (/app/src/index.js:90:7)\n    at async ExLogServer.start (/app/src/index.js:101:7)","timestamp":"2025-05-29 02:10:07:107"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connected successfully\u001b[39m","timestamp":"2025-05-29 02:11:08:118"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-05-29 02:11:08:118"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTimescaleDB connected successfully\u001b[39m","timestamp":"2025-05-29 02:11:08:118"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mElasticsearch connected successfully\u001b[39m","timestamp":"2025-05-29 02:11:08:118"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connected successfully\u001b[39m","timestamp":"2025-05-29 02:12:47:1247"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-05-29 02:12:47:1247"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mElasticsearch connected successfully\u001b[39m","timestamp":"2025-05-29 02:12:47:1247"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTimescaleDB connected successfully\u001b[39m","timestamp":"2025-05-29 02:12:47:1247"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connected successfully\u001b[39m","timestamp":"2025-05-29 02:13:17:1317"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-05-29 02:13:17:1317"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mElasticsearch connected successfully\u001b[39m","timestamp":"2025-05-29 02:13:18:1318"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTimescaleDB connected successfully\u001b[39m","timestamp":"2025-05-29 02:13:18:1318"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll databases connected successfully\u001b[39m","timestamp":"2025-05-29 02:13:18:1318"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections established\u001b[39m","timestamp":"2025-05-29 02:13:18:1318"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExLog API server running on port 5000\u001b[39m","timestamp":"2025-05-29 02:13:18:1318"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-05-29 02:13:18:1318"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM. Starting graceful shutdown...\u001b[39m","timestamp":"2025-05-29 02:22:09:229"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP server closed\u001b[39m","timestamp":"2025-05-29 02:22:09:229"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mMongoDB disconnected\u001b[39m","timestamp":"2025-05-29 02:22:09:229"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections closed\u001b[39m","timestamp":"2025-05-29 02:22:09:229"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connected successfully\u001b[39m","timestamp":"2025-05-29 02:22:13:2213"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-05-29 02:22:13:2213"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mElasticsearch connected successfully\u001b[39m","timestamp":"2025-05-29 02:22:13:2213"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTimescaleDB connected successfully\u001b[39m","timestamp":"2025-05-29 02:22:13:2213"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll databases connected successfully\u001b[39m","timestamp":"2025-05-29 02:22:13:2213"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections established\u001b[39m","timestamp":"2025-05-29 02:22:13:2213"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExLog API server running on port 5000\u001b[39m","timestamp":"2025-05-29 02:22:13:2213"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-05-29 02:22:13:2213"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Command find requires authentication\u001b[39m","method":"POST","stack":"MongoServerError: Command find requires authentication\n    at Connection.sendCommand (/app/node_modules/mongodb/lib/cmap/connection.js:299:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Connection.command (/app/node_modules/mongodb/lib/cmap/connection.js:327:26)\n    at async Server.command (/app/node_modules/mongodb/lib/sdam/server.js:168:29)\n    at async FindOperation.execute (/app/node_modules/mongodb/lib/operations/find.js:36:16)\n    at async tryOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:207:20)\n    at async executeOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:75:16)\n    at async FindCursor._initialize (/app/node_modules/mongodb/lib/cursor/find_cursor.js:61:26)\n    at async FindCursor.cursorInit (/app/node_modules/mongodb/lib/cursor/abstract_cursor.js:633:27)\n    at async FindCursor.fetchBatch (/app/node_modules/mongodb/lib/cursor/abstract_cursor.js:667:13)","timestamp":"2025-05-29 02:24:52:2452","url":"/api/v1/auth/login","userAgent":"curl/8.2.1"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Command find requires authentication\u001b[39m","method":"POST","stack":"MongoServerError: Command find requires authentication\n    at Connection.sendCommand (/app/node_modules/mongodb/lib/cmap/connection.js:299:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Connection.command (/app/node_modules/mongodb/lib/cmap/connection.js:327:26)\n    at async Server.command (/app/node_modules/mongodb/lib/sdam/server.js:168:29)\n    at async FindOperation.execute (/app/node_modules/mongodb/lib/operations/find.js:36:16)\n    at async tryOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:207:20)\n    at async executeOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:75:16)\n    at async FindCursor._initialize (/app/node_modules/mongodb/lib/cursor/find_cursor.js:61:26)\n    at async FindCursor.cursorInit (/app/node_modules/mongodb/lib/cursor/abstract_cursor.js:633:27)\n    at async FindCursor.fetchBatch (/app/node_modules/mongodb/lib/cursor/abstract_cursor.js:667:13)","timestamp":"2025-05-29 02:46:13:4613","url":"/api/v1/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Command find requires authentication\u001b[39m","method":"POST","stack":"MongoServerError: Command find requires authentication\n    at Connection.sendCommand (/app/node_modules/mongodb/lib/cmap/connection.js:299:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Connection.command (/app/node_modules/mongodb/lib/cmap/connection.js:327:26)\n    at async Server.command (/app/node_modules/mongodb/lib/sdam/server.js:168:29)\n    at async FindOperation.execute (/app/node_modules/mongodb/lib/operations/find.js:36:16)\n    at async tryOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:207:20)\n    at async executeOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:75:16)\n    at async FindCursor._initialize (/app/node_modules/mongodb/lib/cursor/find_cursor.js:61:26)\n    at async FindCursor.cursorInit (/app/node_modules/mongodb/lib/cursor/abstract_cursor.js:633:27)\n    at async FindCursor.fetchBatch (/app/node_modules/mongodb/lib/cursor/abstract_cursor.js:667:13)","timestamp":"2025-05-29 02:46:21:4621","url":"/api/v1/auth/login","userAgent":"curl/8.2.1"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Command find requires authentication\u001b[39m","method":"POST","stack":"MongoServerError: Command find requires authentication\n    at Connection.sendCommand (/app/node_modules/mongodb/lib/cmap/connection.js:299:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Connection.command (/app/node_modules/mongodb/lib/cmap/connection.js:327:26)\n    at async Server.command (/app/node_modules/mongodb/lib/sdam/server.js:168:29)\n    at async FindOperation.execute (/app/node_modules/mongodb/lib/operations/find.js:36:16)\n    at async tryOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:207:20)\n    at async executeOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:75:16)\n    at async FindCursor._initialize (/app/node_modules/mongodb/lib/cursor/find_cursor.js:61:26)\n    at async FindCursor.cursorInit (/app/node_modules/mongodb/lib/cursor/abstract_cursor.js:633:27)\n    at async FindCursor.fetchBatch (/app/node_modules/mongodb/lib/cursor/abstract_cursor.js:667:13)","timestamp":"2025-05-29 02:46:48:4648","url":"/api/v1/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Command find requires authentication\u001b[39m","method":"POST","stack":"MongoServerError: Command find requires authentication\n    at Connection.sendCommand (/app/node_modules/mongodb/lib/cmap/connection.js:299:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Connection.command (/app/node_modules/mongodb/lib/cmap/connection.js:327:26)\n    at async Server.command (/app/node_modules/mongodb/lib/sdam/server.js:168:29)\n    at async FindOperation.execute (/app/node_modules/mongodb/lib/operations/find.js:36:16)\n    at async tryOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:207:20)\n    at async executeOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:75:16)\n    at async FindCursor._initialize (/app/node_modules/mongodb/lib/cursor/find_cursor.js:61:26)\n    at async FindCursor.cursorInit (/app/node_modules/mongodb/lib/cursor/abstract_cursor.js:633:27)\n    at async FindCursor.fetchBatch (/app/node_modules/mongodb/lib/cursor/abstract_cursor.js:667:13)","timestamp":"2025-05-29 02:47:14:4714","url":"/api/v1/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM. Starting graceful shutdown...\u001b[39m","timestamp":"2025-05-29 02:50:21:5021"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP server closed\u001b[39m","timestamp":"2025-05-29 02:50:21:5021"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mMongoDB disconnected\u001b[39m","timestamp":"2025-05-29 02:50:21:5021"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections closed\u001b[39m","timestamp":"2025-05-29 02:50:21:5021"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connected successfully\u001b[39m","timestamp":"2025-05-29 02:50:26:5026"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-05-29 02:50:26:5026"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTimescaleDB connected successfully\u001b[39m","timestamp":"2025-05-29 02:50:26:5026"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mElasticsearch connected successfully\u001b[39m","timestamp":"2025-05-29 02:50:26:5026"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll databases connected successfully\u001b[39m","timestamp":"2025-05-29 02:50:26:5026"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections established\u001b[39m","timestamp":"2025-05-29 02:50:26:5026"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExLog API server running on port 5000\u001b[39m","timestamp":"2025-05-29 02:50:26:5026"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-05-29 02:50:26:5026"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Command find requires authentication\u001b[39m","method":"POST","stack":"MongoServerError: Command find requires authentication\n    at Connection.sendCommand (/app/node_modules/mongodb/lib/cmap/connection.js:299:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Connection.command (/app/node_modules/mongodb/lib/cmap/connection.js:327:26)\n    at async Server.command (/app/node_modules/mongodb/lib/sdam/server.js:168:29)\n    at async FindOperation.execute (/app/node_modules/mongodb/lib/operations/find.js:36:16)\n    at async tryOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:207:20)\n    at async executeOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:75:16)\n    at async FindCursor._initialize (/app/node_modules/mongodb/lib/cursor/find_cursor.js:61:26)\n    at async FindCursor.cursorInit (/app/node_modules/mongodb/lib/cursor/abstract_cursor.js:633:27)\n    at async FindCursor.fetchBatch (/app/node_modules/mongodb/lib/cursor/abstract_cursor.js:667:13)","timestamp":"2025-05-29 02:50:41:5041","url":"/api/v1/auth/login","userAgent":"curl/8.2.1"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM, shutting down WebSocket server...\u001b[39m","timestamp":"2025-05-29 02:50:48:5048"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM. Starting graceful shutdown...\u001b[39m","timestamp":"2025-05-29 02:50:49:5049"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP server closed\u001b[39m","timestamp":"2025-05-29 02:50:49:5049"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mMongoDB disconnected\u001b[39m","timestamp":"2025-05-29 02:50:49:5049"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections closed\u001b[39m","timestamp":"2025-05-29 02:50:49:5049"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mWebSocket server started on port 5001\u001b[39m","timestamp":"2025-05-29 02:51:14:5114"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connected successfully\u001b[39m","timestamp":"2025-05-29 02:51:19:5119"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-05-29 02:51:19:5119"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTimescaleDB connected successfully\u001b[39m","timestamp":"2025-05-29 02:51:20:5120"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to Elasticsearch: connect ECONNREFUSED 172.18.0.5:9200\u001b[39m","meta":{"headers":{},"meta":{"aborted":false,"attempts":3,"connection":{"_openRequests":0,"deadCount":0,"headers":{"user-agent":"elasticsearch-js/8.18.2 (linux 5.15.167.4-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"id":"http://elasticsearch:9200/","maxEventListeners":100,"pool":{"_events":{},"_eventsCount":1},"resurrectTimeout":0,"timeout":30000,"tls":null,"url":"http://elasticsearch:9200/","weight":1000},"context":null,"name":"elasticsearch-js","request":{"id":1,"options":{},"params":{"headers":{"accept":"application/vnd.elasticsearch+json; compatible-with=8,text/plain","user-agent":"elasticsearch-js/8.18.2 (linux 5.15.167.4-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"method":"HEAD","path":"/","querystring":""}}},"statusCode":0,"warnings":null},"name":"ConnectionError","options":{"redaction":{"additionalKeys":[],"type":"replace"}},"stack":"ConnectionError: connect ECONNREFUSED 172.18.0.5:9200\n    at SniffingTransport._request (/app/node_modules/@elastic/transport/lib/Transport.js:595:31)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/node_modules/@elastic/transport/lib/Transport.js:627:32\n    at async SniffingTransport.request (/app/node_modules/@elastic/transport/lib/Transport.js:623:20)\n    at async Client.PingApi [as ping] (/app/node_modules/@elastic/elasticsearch/lib/api/api/ping.js:41:12)\n    at async DatabaseManager.connectElasticsearch (/app/src/config/database.js:73:7)\n    at async Promise.all (index 2)\n    at async DatabaseManager.connectAll (/app/src/config/database.js:110:7)\n    at async ExLogServer.connectDatabases (/app/src/index.js:90:7)\n    at async ExLogServer.start (/app/src/index.js:101:7)","timestamp":"2025-05-29 02:51:27:5127"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connected successfully\u001b[39m","timestamp":"2025-05-29 02:52:25:5225"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-05-29 02:52:25:5225"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTimescaleDB connected successfully\u001b[39m","timestamp":"2025-05-29 02:52:25:5225"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mElasticsearch connected successfully\u001b[39m","timestamp":"2025-05-29 02:52:25:5225"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m4 databases connected successfully\u001b[39m","timestamp":"2025-05-29 02:52:25:5225"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections established\u001b[39m","timestamp":"2025-05-29 02:52:25:5225"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExLog API server running on port 5000\u001b[39m","timestamp":"2025-05-29 02:52:25:5225"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-05-29 02:52:25:5225"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Command find requires authentication\u001b[39m","method":"POST","stack":"MongoServerError: Command find requires authentication\n    at Connection.sendCommand (/app/node_modules/mongodb/lib/cmap/connection.js:299:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Connection.command (/app/node_modules/mongodb/lib/cmap/connection.js:327:26)\n    at async Server.command (/app/node_modules/mongodb/lib/sdam/server.js:168:29)\n    at async FindOperation.execute (/app/node_modules/mongodb/lib/operations/find.js:36:16)\n    at async tryOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:207:20)\n    at async executeOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:75:16)\n    at async FindCursor._initialize (/app/node_modules/mongodb/lib/cursor/find_cursor.js:61:26)\n    at async FindCursor.cursorInit (/app/node_modules/mongodb/lib/cursor/abstract_cursor.js:633:27)\n    at async FindCursor.fetchBatch (/app/node_modules/mongodb/lib/cursor/abstract_cursor.js:667:13)","timestamp":"2025-05-29 02:52:36:5236","url":"/api/v1/auth/login","userAgent":"curl/8.2.1"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Command find requires authentication\u001b[39m","method":"POST","stack":"MongoServerError: Command find requires authentication\n    at Connection.sendCommand (/app/node_modules/mongodb/lib/cmap/connection.js:299:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Connection.command (/app/node_modules/mongodb/lib/cmap/connection.js:327:26)\n    at async Server.command (/app/node_modules/mongodb/lib/sdam/server.js:168:29)\n    at async FindOperation.execute (/app/node_modules/mongodb/lib/operations/find.js:36:16)\n    at async tryOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:207:20)\n    at async executeOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:75:16)\n    at async FindCursor._initialize (/app/node_modules/mongodb/lib/cursor/find_cursor.js:61:26)\n    at async FindCursor.cursorInit (/app/node_modules/mongodb/lib/cursor/abstract_cursor.js:633:27)\n    at async FindCursor.fetchBatch (/app/node_modules/mongodb/lib/cursor/abstract_cursor.js:667:13)","timestamp":"2025-05-29 02:55:49:5549","url":"/api/v1/auth/login","userAgent":"curl/8.2.1"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Command find requires authentication\u001b[39m","method":"POST","stack":"MongoServerError: Command find requires authentication\n    at Connection.sendCommand (/app/node_modules/mongodb/lib/cmap/connection.js:299:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Connection.command (/app/node_modules/mongodb/lib/cmap/connection.js:327:26)\n    at async Server.command (/app/node_modules/mongodb/lib/sdam/server.js:168:29)\n    at async FindOperation.execute (/app/node_modules/mongodb/lib/operations/find.js:36:16)\n    at async tryOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:207:20)\n    at async executeOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:75:16)\n    at async FindCursor._initialize (/app/node_modules/mongodb/lib/cursor/find_cursor.js:61:26)\n    at async FindCursor.cursorInit (/app/node_modules/mongodb/lib/cursor/abstract_cursor.js:633:27)\n    at async FindCursor.fetchBatch (/app/node_modules/mongodb/lib/cursor/abstract_cursor.js:667:13)","timestamp":"2025-05-29 02:56:25:5625","url":"/api/v1/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM. Starting graceful shutdown...\u001b[39m","timestamp":"2025-05-29 02:58:28:5828"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP server closed\u001b[39m","timestamp":"2025-05-29 02:58:28:5828"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mMongoDB disconnected\u001b[39m","timestamp":"2025-05-29 02:58:28:5828"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections closed\u001b[39m","timestamp":"2025-05-29 02:58:28:5828"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connected successfully\u001b[39m","timestamp":"2025-05-29 02:58:30:5830"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-05-29 02:58:30:5830"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mElasticsearch connected successfully\u001b[39m","timestamp":"2025-05-29 02:58:30:5830"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTimescaleDB connected successfully\u001b[39m","timestamp":"2025-05-29 02:58:30:5830"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m4 databases connected successfully\u001b[39m","timestamp":"2025-05-29 02:58:30:5830"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections established\u001b[39m","timestamp":"2025-05-29 02:58:30:5830"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExLog API server running on port 5000\u001b[39m","timestamp":"2025-05-29 02:58:30:5830"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-05-29 02:58:30:5830"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Command find requires authentication\u001b[39m","method":"POST","stack":"MongoServerError: Command find requires authentication\n    at Connection.sendCommand (/app/node_modules/mongodb/lib/cmap/connection.js:299:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Connection.command (/app/node_modules/mongodb/lib/cmap/connection.js:327:26)\n    at async Server.command (/app/node_modules/mongodb/lib/sdam/server.js:168:29)\n    at async FindOperation.execute (/app/node_modules/mongodb/lib/operations/find.js:36:16)\n    at async tryOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:207:20)\n    at async executeOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:75:16)\n    at async FindCursor._initialize (/app/node_modules/mongodb/lib/cursor/find_cursor.js:61:26)\n    at async FindCursor.cursorInit (/app/node_modules/mongodb/lib/cursor/abstract_cursor.js:633:27)\n    at async FindCursor.fetchBatch (/app/node_modules/mongodb/lib/cursor/abstract_cursor.js:667:13)","timestamp":"2025-05-29 02:58:43:5843","url":"/api/v1/auth/login","userAgent":"curl/8.2.1"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM. Starting graceful shutdown...\u001b[39m","timestamp":"2025-05-29 02:59:23:5923"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP server closed\u001b[39m","timestamp":"2025-05-29 02:59:23:5923"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mMongoDB disconnected\u001b[39m","timestamp":"2025-05-29 02:59:23:5923"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections closed\u001b[39m","timestamp":"2025-05-29 02:59:23:5923"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connected successfully\u001b[39m","timestamp":"2025-05-29 02:59:25:5925"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-05-29 02:59:25:5925"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mElasticsearch connected successfully\u001b[39m","timestamp":"2025-05-29 02:59:25:5925"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTimescaleDB connected successfully\u001b[39m","timestamp":"2025-05-29 02:59:25:5925"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m4 databases connected successfully\u001b[39m","timestamp":"2025-05-29 02:59:25:5925"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections established\u001b[39m","timestamp":"2025-05-29 02:59:25:5925"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExLog API server running on port 5000\u001b[39m","timestamp":"2025-05-29 02:59:25:5925"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-05-29 02:59:25:5925"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Command find requires authentication\u001b[39m","method":"POST","stack":"MongoServerError: Command find requires authentication\n    at Connection.sendCommand (/app/node_modules/mongodb/lib/cmap/connection.js:299:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Connection.command (/app/node_modules/mongodb/lib/cmap/connection.js:327:26)\n    at async Server.command (/app/node_modules/mongodb/lib/sdam/server.js:168:29)\n    at async FindOperation.execute (/app/node_modules/mongodb/lib/operations/find.js:36:16)\n    at async tryOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:207:20)\n    at async executeOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:75:16)\n    at async FindCursor._initialize (/app/node_modules/mongodb/lib/cursor/find_cursor.js:61:26)\n    at async FindCursor.cursorInit (/app/node_modules/mongodb/lib/cursor/abstract_cursor.js:633:27)\n    at async FindCursor.fetchBatch (/app/node_modules/mongodb/lib/cursor/abstract_cursor.js:667:13)","timestamp":"2025-05-29 02:59:40:5940","url":"/api/v1/auth/login","userAgent":"curl/8.2.1"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mMongoDB disconnected\u001b[39m","timestamp":"2025-05-29 03:00:25:025"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM. Starting graceful shutdown...\u001b[39m","timestamp":"2025-05-29 03:01:05:15"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP server closed\u001b[39m","timestamp":"2025-05-29 03:01:05:15"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mMongoDB disconnected\u001b[39m","timestamp":"2025-05-29 03:01:05:15"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections closed\u001b[39m","timestamp":"2025-05-29 03:01:05:15"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connected successfully\u001b[39m","timestamp":"2025-05-29 03:01:07:17"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-05-29 03:01:07:17"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mElasticsearch connected successfully\u001b[39m","timestamp":"2025-05-29 03:01:07:17"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTimescaleDB connected successfully\u001b[39m","timestamp":"2025-05-29 03:01:07:17"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m4 databases connected successfully\u001b[39m","timestamp":"2025-05-29 03:01:07:17"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections established\u001b[39m","timestamp":"2025-05-29 03:01:07:17"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExLog API server running on port 5000\u001b[39m","timestamp":"2025-05-29 03:01:07:17"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-05-29 03:01:07:17"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Invalid email or password\u001b[39m","method":"POST","stack":"Error: Invalid email or password\n    at /app/src/routes/auth.js:156:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-29 03:01:34:134","url":"/api/v1/auth/login","userAgent":"curl/8.2.1"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: User validation failed: email: Please enter a valid email\u001b[39m","method":"POST","stack":"ValidationError: User validation failed: email: Please enter a valid email\n    at Document.invalidate (/app/node_modules/mongoose/lib/document.js:3343:32)\n    at /app/node_modules/mongoose/lib/document.js:3104:17\n    at /app/node_modules/mongoose/lib/schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-29 03:02:10:210","url":"/api/v1/auth/login","userAgent":"curl/8.2.1"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM. Starting graceful shutdown...\u001b[39m","timestamp":"2025-05-29 03:02:38:238"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP server closed\u001b[39m","timestamp":"2025-05-29 03:02:38:238"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mMongoDB disconnected\u001b[39m","timestamp":"2025-05-29 03:02:38:238"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections closed\u001b[39m","timestamp":"2025-05-29 03:02:38:238"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connected successfully\u001b[39m","timestamp":"2025-05-29 03:02:43:243"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-05-29 03:02:43:243"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mElasticsearch connected successfully\u001b[39m","timestamp":"2025-05-29 03:02:43:243"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTimescaleDB connected successfully\u001b[39m","timestamp":"2025-05-29 03:02:43:243"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m4 databases connected successfully\u001b[39m","timestamp":"2025-05-29 03:02:43:243"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections established\u001b[39m","timestamp":"2025-05-29 03:02:43:243"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExLog API server running on port 5000\u001b[39m","timestamp":"2025-05-29 03:02:43:243"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-05-29 03:02:43:243"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-05-29 03:02:47:247"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-05-29 03:03:13:313"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-05-29 03:04:11:411"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM. Starting graceful shutdown...\u001b[39m","timestamp":"2025-05-29 03:06:53:653"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP server closed\u001b[39m","timestamp":"2025-05-29 03:06:53:653"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mMongoDB disconnected\u001b[39m","timestamp":"2025-05-29 03:06:53:653"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections closed\u001b[39m","timestamp":"2025-05-29 03:06:53:653"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connected successfully\u001b[39m","timestamp":"2025-05-29 03:06:56:656"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-05-29 03:06:56:656"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mElasticsearch connected successfully\u001b[39m","timestamp":"2025-05-29 03:06:57:657"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTimescaleDB connected successfully\u001b[39m","timestamp":"2025-05-29 03:06:57:657"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m4 databases connected successfully\u001b[39m","timestamp":"2025-05-29 03:06:57:657"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections established\u001b[39m","timestamp":"2025-05-29 03:06:57:657"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExLog API server running on port 5000\u001b[39m","timestamp":"2025-05-29 03:06:57:657"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-05-29 03:06:57:657"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-05-29 03:07:12:712"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged out: <EMAIL>\u001b[39m","timestamp":"2025-05-29 03:07:51:751"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-05-29 03:07:53:753"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-05-29 03:10:35:1035"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged out: <EMAIL>\u001b[39m","timestamp":"2025-05-29 03:11:28:1128"}
