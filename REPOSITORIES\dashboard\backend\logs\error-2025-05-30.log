{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to Elasticsearch: connect ECONNREFUSED 172.18.0.3:9200\u001b[39m","meta":{"headers":{},"meta":{"aborted":false,"attempts":3,"connection":{"_openRequests":0,"deadCount":0,"headers":{"user-agent":"elasticsearch-js/8.18.2 (linux **********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"id":"http://elasticsearch:9200/","maxEventListeners":100,"pool":{"_events":{},"_eventsCount":1},"resurrectTimeout":0,"timeout":30000,"tls":null,"url":"http://elasticsearch:9200/","weight":1000},"context":null,"name":"elasticsearch-js","request":{"id":1,"options":{},"params":{"headers":{"accept":"application/vnd.elasticsearch+json; compatible-with=8,text/plain","user-agent":"elasticsearch-js/8.18.2 (linux **********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"method":"HEAD","path":"/","querystring":""}}},"statusCode":0,"warnings":null},"name":"ConnectionError","options":{"redaction":{"additionalKeys":[],"type":"replace"}},"stack":"ConnectionError: connect ECONNREFUSED 172.18.0.3:9200\n    at SniffingTransport._request (/app/node_modules/@elastic/transport/lib/Transport.js:595:31)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/node_modules/@elastic/transport/lib/Transport.js:627:32\n    at async SniffingTransport.request (/app/node_modules/@elastic/transport/lib/Transport.js:623:20)\n    at async Client.PingApi [as ping] (/app/node_modules/@elastic/elasticsearch/lib/api/api/ping.js:41:12)\n    at async DatabaseManager.connectElasticsearch (/app/src/config/database.js:73:7)\n    at async Promise.allSettled (index 2)\n    at async DatabaseManager.connectAll (/app/src/config/database.js:109:21)\n    at async ExLogServer.connectDatabases (/app/src/index.js:90:7)\n    at async ExLogServer.start (/app/src/index.js:101:7)","timestamp":"2025-05-30 03:00:23:023"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to Elasticsearch: connect ECONNREFUSED **********:9200\u001b[39m","meta":{"headers":{},"meta":{"aborted":false,"attempts":3,"connection":{"_openRequests":0,"deadCount":0,"headers":{"user-agent":"elasticsearch-js/8.18.2 (linux **********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"id":"http://elasticsearch:9200/","maxEventListeners":100,"pool":{"_events":{},"_eventsCount":1},"resurrectTimeout":0,"timeout":30000,"tls":null,"url":"http://elasticsearch:9200/","weight":1000},"context":null,"name":"elasticsearch-js","request":{"id":1,"options":{},"params":{"headers":{"accept":"application/vnd.elasticsearch+json; compatible-with=8,text/plain","user-agent":"elasticsearch-js/8.18.2 (linux **********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"method":"HEAD","path":"/","querystring":""}}},"statusCode":0,"warnings":null},"name":"ConnectionError","options":{"redaction":{"additionalKeys":[],"type":"replace"}},"stack":"ConnectionError: connect ECONNREFUSED **********:9200\n    at SniffingTransport._request (/app/node_modules/@elastic/transport/lib/Transport.js:595:31)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/node_modules/@elastic/transport/lib/Transport.js:627:32\n    at async SniffingTransport.request (/app/node_modules/@elastic/transport/lib/Transport.js:623:20)\n    at async Client.PingApi [as ping] (/app/node_modules/@elastic/elasticsearch/lib/api/api/ping.js:41:12)\n    at async DatabaseManager.connectElasticsearch (/app/src/config/database.js:73:7)\n    at async Promise.allSettled (index 2)\n    at async DatabaseManager.connectAll (/app/src/config/database.js:109:21)\n    at async ExLogServer.connectDatabases (/app/src/index.js:135:7)\n    at async ExLogServer.start (/app/src/index.js:146:7)","timestamp":"2025-05-30 03:47:04:474"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: User with this email or username already exists\u001b[39m","method":"POST","stack":"Error: User with this email or username already exists\n    at /app/src/routes/auth.js:75:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 04:07:47:747","url":"/api/v1/auth/register","userAgent":"axios/1.9.0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: User with this email or username already exists\u001b[39m","method":"POST","stack":"Error: User with this email or username already exists\n    at /app/src/routes/auth.js:75:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 04:08:42:842","url":"/api/v1/auth/register","userAgent":"axios/1.9.0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: User with this email or username already exists\u001b[39m","method":"POST","stack":"Error: User with this email or username already exists\n    at /app/src/routes/auth.js:75:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 04:09:51:951","url":"/api/v1/auth/register","userAgent":"axios/1.9.0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: User with this email or username already exists\u001b[39m","method":"POST","stack":"Error: User with this email or username already exists\n    at /app/src/routes/auth.js:75:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 04:15:38:1538","url":"/api/v1/auth/register","userAgent":"axios/1.9.0"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to Elasticsearch: connect ECONNREFUSED **********:9200\u001b[39m","meta":{"headers":{},"meta":{"aborted":false,"attempts":3,"connection":{"_openRequests":0,"deadCount":0,"headers":{"user-agent":"elasticsearch-js/8.18.2 (linux **********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"id":"http://elasticsearch:9200/","maxEventListeners":100,"pool":{"_events":{},"_eventsCount":1},"resurrectTimeout":0,"timeout":30000,"tls":null,"url":"http://elasticsearch:9200/","weight":1000},"context":null,"name":"elasticsearch-js","request":{"id":1,"options":{},"params":{"headers":{"accept":"application/vnd.elasticsearch+json; compatible-with=8,text/plain","user-agent":"elasticsearch-js/8.18.2 (linux **********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"method":"HEAD","path":"/","querystring":""}}},"statusCode":0,"warnings":null},"name":"ConnectionError","options":{"redaction":{"additionalKeys":[],"type":"replace"}},"stack":"ConnectionError: connect ECONNREFUSED **********:9200\n    at SniffingTransport._request (/app/node_modules/@elastic/transport/lib/Transport.js:595:31)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/node_modules/@elastic/transport/lib/Transport.js:627:32\n    at async SniffingTransport.request (/app/node_modules/@elastic/transport/lib/Transport.js:623:20)\n    at async Client.PingApi [as ping] (/app/node_modules/@elastic/elasticsearch/lib/api/api/ping.js:41:12)\n    at async DatabaseManager.connectElasticsearch (/app/src/config/database.js:73:7)\n    at async Promise.allSettled (index 2)\n    at async DatabaseManager.connectAll (/app/src/config/database.js:109:21)\n    at async ExLogServer.connectDatabases (/app/src/index.js:135:7)\n    at async ExLogServer.start (/app/src/index.js:146:7)","timestamp":"2025-05-30 04:23:46:2346"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 04:51:20:5120","url":"/api/v1/logs","userAgent":"curl/8.2.1","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 04:54:04:544","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 04:54:04:544","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 04:54:04:544","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 04:56:46:5646","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 04:56:46:5646","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 04:56:46:5646","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"code":11000,"errorLabelSet":{},"errorResponse":{"code":11000,"errmsg":"E11000 duplicate key error collection: exlog.logs index: logId_1 dup key: { logId: \"5b72c7cb-8b97-4b0a-84b4-d59173d3bd7e\" }","index":0,"keyPattern":{"logId":1},"keyValue":{"logId":"5b72c7cb-8b97-4b0a-84b4-d59173d3bd7e"}},"index":0,"keyPattern":{"logId":1},"keyValue":{"logId":"5b72c7cb-8b97-4b0a-84b4-d59173d3bd7e"},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to process log 5b72c7cb-8b97-4b0a-84b4-d59173d3bd7e: E11000 duplicate key error collection: exlog.logs index: logId_1 dup key: { logId: \"5b72c7cb-8b97-4b0a-84b4-d59173d3bd7e\" }\u001b[39m","stack":"MongoServerError: E11000 duplicate key error collection: exlog.logs index: logId_1 dup key: { logId: \"5b72c7cb-8b97-4b0a-84b4-d59173d3bd7e\" }\n    at InsertOneOperation.execute (/app/node_modules/mongodb/lib/operations/insert.js:51:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async tryOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:207:20)\n    at async executeOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:75:16)\n    at async Collection.insertOne (/app/node_modules/mongodb/lib/collection.js:157:16)","timestamp":"2025-05-30 05:00:46:046"}
{"code":11000,"errorLabelSet":{},"errorResponse":{"code":11000,"errmsg":"E11000 duplicate key error collection: exlog.logs index: logId_1 dup key: { logId: \"5b72c7cb-8b97-4b0a-84b4-d59173d3bd7e\" }","index":0,"keyPattern":{"logId":1},"keyValue":{"logId":"5b72c7cb-8b97-4b0a-84b4-d59173d3bd7e"}},"index":0,"keyPattern":{"logId":1},"keyValue":{"logId":"5b72c7cb-8b97-4b0a-84b4-d59173d3bd7e"},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to process log 5b72c7cb-8b97-4b0a-84b4-d59173d3bd7e: E11000 duplicate key error collection: exlog.logs index: logId_1 dup key: { logId: \"5b72c7cb-8b97-4b0a-84b4-d59173d3bd7e\" }\u001b[39m","stack":"MongoServerError: E11000 duplicate key error collection: exlog.logs index: logId_1 dup key: { logId: \"5b72c7cb-8b97-4b0a-84b4-d59173d3bd7e\" }\n    at InsertOneOperation.execute (/app/node_modules/mongodb/lib/operations/insert.js:51:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async tryOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:207:20)\n    at async executeOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:75:16)\n    at async Collection.insertOne (/app/node_modules/mongodb/lib/collection.js:157:16)","timestamp":"2025-05-30 05:00:46:046"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:09:40:940","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:10:59:1059","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:11:09:119","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:11:09:119","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:11:19:1119","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:11:19:1119","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:11:29:1129","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:11:29:1129","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:15:06:156","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:15:06:156","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:15:57:1557","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:15:57:1557","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:16:11:1611","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:16:11:1611","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:16:11:1611","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:16:11:1611","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:16:11:1611","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:16:21:1621","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:16:31:1631","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:16:31:1631","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:17:41:1741","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:17:42:1742","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:17:55:1755","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:17:55:1755","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:17:55:1755","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:17:55:1755","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:17:55:1755","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:18:05:185","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:18:15:1815","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:18:15:1815","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:18:49:1849","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:18:49:1849","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:18:49:1849","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:18:49:1849","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:18:49:1849","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:19:00:190","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:19:10:1910","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:19:10:1910","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:19:48:1948","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:19:48:1948","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:19:48:1948","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:19:49:1949","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:19:49:1949","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:19:59:1959","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:20:09:209","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:20:09:209","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:20:24:2024","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:20:34:2034","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:20:34:2034","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:20:44:2044","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:20:44:2044","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:25:48:2548","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:25:48:2548","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:26:02:262","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:26:03:263","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:26:03:263","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:26:03:263","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:26:03:263","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:26:13:2613","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:26:23:2623","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:26:23:2623","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:27:27:2727","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:27:27:2727","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:27:27:2727","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:27:27:2727","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:27:27:2727","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:27:37:2737","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:27:47:2747","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:27:47:2747","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:29:03:293","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:29:04:294","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:29:16:2916","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:29:16:2916","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:29:16:2916","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:29:16:2916","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:29:26:2926","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:29:36:2936","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:29:36:2936","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:30:43:3043","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:30:43:3043","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:30:51:3051","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:30:51:3051","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:30:52:3052","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:30:52:3052","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:31:02:312","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:31:12:3112","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:31:12:3112","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:33:17:3317","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:33:17:3317","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:33:25:3325","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:33:26:3326","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:33:26:3326","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:33:26:3326","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:33:36:3336","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:33:46:3346","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:33:46:3346","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:34:42:3442","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:34:42:3442","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:34:54:3454","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:34:54:3454","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:34:54:3454","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:34:54:3454","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:35:05:355","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:35:15:3515","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:35:15:3515","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:36:16:3616","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:36:16:3616","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:36:33:3633","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:36:33:3633","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:36:33:3633","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:36:33:3633","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:36:43:3643","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:36:53:3653","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:36:53:3653","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:37:54:3754","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:37:54:3754","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:37:54:3754","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:37:55:3755","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:40:51:4051","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:40:51:4051","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:40:51:4051","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:40:51:4051","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:40:51:4051","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:40:52:4052","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:40:52:4052","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:40:52:4052","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:40:52:4052","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:40:53:4053","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:40:53:4053","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:40:53:4053","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:40:53:4053","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:56:14:5614","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:56:24:5624","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:56:24:5624","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:56:34:5634","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:56:44:5644","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:56:44:5644","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:56:54:5654","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:56:54:5654","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:57:04:574","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:57:04:574","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:57:14:5714","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 05:57:14:5714","url":"/api/v1/logs","userAgent":"PythonLoggingAgent/1.1.0","userId":"6837cddc74fba9b25bd861e0"}
{"address":"**********","code":"ECONNREFUSED","errno":-111,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to TimescaleDB: connect ECONNREFUSED **********:5432\u001b[39m","port":5432,"stack":"Error: connect ECONNREFUSED **********:5432\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)","syscall":"connect","timestamp":"2025-05-30 20:55:49:5549"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to Elasticsearch: connect ECONNREFUSED **********:9200\u001b[39m","meta":{"headers":{},"meta":{"aborted":false,"attempts":3,"connection":{"_openRequests":0,"deadCount":0,"headers":{"user-agent":"elasticsearch-js/8.18.2 (linux **********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"id":"http://elasticsearch:9200/","maxEventListeners":100,"pool":{"_events":{},"_eventsCount":1},"resurrectTimeout":0,"timeout":30000,"tls":null,"url":"http://elasticsearch:9200/","weight":1000},"context":null,"name":"elasticsearch-js","request":{"id":1,"options":{},"params":{"headers":{"accept":"application/vnd.elasticsearch+json; compatible-with=8,text/plain","user-agent":"elasticsearch-js/8.18.2 (linux **********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"method":"HEAD","path":"/","querystring":""}}},"statusCode":0,"warnings":null},"name":"ConnectionError","options":{"redaction":{"additionalKeys":[],"type":"replace"}},"stack":"ConnectionError: connect ECONNREFUSED **********:9200\n    at SniffingTransport._request (/app/node_modules/@elastic/transport/lib/Transport.js:595:31)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/node_modules/@elastic/transport/lib/Transport.js:627:32\n    at async SniffingTransport.request (/app/node_modules/@elastic/transport/lib/Transport.js:623:20)\n    at async Client.PingApi [as ping] (/app/node_modules/@elastic/elasticsearch/lib/api/api/ping.js:41:12)\n    at async DatabaseManager.connectElasticsearch (/app/src/config/database.js:73:7)\n    at async Promise.allSettled (index 2)\n    at async DatabaseManager.connectAll (/app/src/config/database.js:109:21)\n    at async ExLogServer.connectDatabases (/app/src/index.js:135:7)\n    at async ExLogServer.start (/app/src/index.js:146:7)","timestamp":"2025-05-30 20:55:56:5556"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 21:27:05:275","url":"/api/v1/logs?page=1&limit=50&search=test&source=&logLevel=&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-05-30 21:27:20:2720","url":"/api/v1/logs?page=1&limit=50&source=&logLevel=&host=&search=test","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
