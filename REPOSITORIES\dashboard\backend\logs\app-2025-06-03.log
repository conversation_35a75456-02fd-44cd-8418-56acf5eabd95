{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mWebSocket server started on port 5001\u001b[39m","timestamp":"2025-06-03 01:58:25:5825"}
{"address":"**********","code":"ECONNREFUSED","errno":-111,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to TimescaleDB: connect ECONNREFUSED **********:5432\u001b[39m","port":5432,"stack":"Error: connect ECONNREFUSED **********:5432\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)","syscall":"connect","timestamp":"2025-06-03 01:58:29:5829"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m<PERSON>ed<PERSON> connected successfully\u001b[39m","timestamp":"2025-06-03 01:58:29:5829"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-06-03 01:58:34:5834"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to Elasticsearch: connect ECONNREFUSED **********:9200\u001b[39m","meta":{"headers":{},"meta":{"aborted":false,"attempts":3,"connection":{"_openRequests":0,"deadCount":0,"headers":{"user-agent":"elasticsearch-js/8.18.2 (linux **********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"id":"http://elasticsearch:9200/","maxEventListeners":100,"pool":{"_events":{},"_eventsCount":1},"resurrectTimeout":0,"timeout":30000,"tls":null,"url":"http://elasticsearch:9200/","weight":1000},"context":null,"name":"elasticsearch-js","request":{"id":1,"options":{},"params":{"headers":{"accept":"application/vnd.elasticsearch+json; compatible-with=8,text/plain","user-agent":"elasticsearch-js/8.18.2 (linux **********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"method":"HEAD","path":"/","querystring":""}}},"statusCode":0,"warnings":null},"name":"ConnectionError","options":{"redaction":{"additionalKeys":[],"type":"replace"}},"stack":"ConnectionError: connect ECONNREFUSED **********:9200\n    at SniffingTransport._request (/app/node_modules/@elastic/transport/lib/Transport.js:595:31)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/node_modules/@elastic/transport/lib/Transport.js:627:32\n    at async SniffingTransport.request (/app/node_modules/@elastic/transport/lib/Transport.js:623:20)\n    at async Client.PingApi [as ping] (/app/node_modules/@elastic/elasticsearch/lib/api/api/ping.js:41:12)\n    at async DatabaseManager.connectElasticsearch (/app/src/config/database.js:73:7)\n    at async Promise.allSettled (index 2)\n    at async DatabaseManager.connectAll (/app/src/config/database.js:109:21)\n    at async ExLogServer.connectDatabases (/app/src/index.js:135:7)\n    at async ExLogServer.start (/app/src/index.js:146:7)","timestamp":"2025-06-03 01:58:39:5839"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m2 databases connected successfully\u001b[39m","timestamp":"2025-06-03 01:58:39:5839"}
{"0":"connect ECONNREFUSED **********:5432","1":"connect ECONNREFUSED **********:9200","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m2 databases failed to connect:\u001b[39m","timestamp":"2025-06-03 01:58:39:5839"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections established\u001b[39m","timestamp":"2025-06-03 01:58:39:5839"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI documentation enabled at /api/docs\u001b[39m","timestamp":"2025-06-03 01:58:39:5839"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExLog API server running on port 5000\u001b[39m","timestamp":"2025-06-03 01:58:39:5839"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-06-03 01:58:39:5839"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 01:58:45:5845"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683e56c56d7889b16ed861e0\u001b[39m","timestamp":"2025-06-03 02:01:10:110"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-03 02:01:10:110","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683e56c56d7889b16ed861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683e56c56d7889b16ed861e0\u001b[39m","timestamp":"2025-06-03 02:01:17:117"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 9 logs, failed 0 logs from agent 683e56c56d7889b16ed861e0\u001b[39m","timestamp":"2025-06-03 02:01:22:122"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683e56c56d7889b16ed861e0\u001b[39m","timestamp":"2025-06-03 02:01:31:131"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683e56c56d7889b16ed861e0\u001b[39m","timestamp":"2025-06-03 02:01:32:132"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 02:09:21:921"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Invalid email or password\u001b[39m","method":"POST","stack":"Error: Invalid email or password\n    at /app/src/routes/auth.js:180:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-03 02:11:24:1124","url":"/api/v1/auth/login","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.22621.4391"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Invalid email or password\u001b[39m","method":"POST","stack":"Error: Invalid email or password\n    at /app/src/routes/auth.js:199:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-03 02:11:39:1139","url":"/api/v1/auth/login","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.22621.4391"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 02:12:01:121"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 02:12:10:1210"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM. Starting graceful shutdown...\u001b[39m","timestamp":"2025-06-03 02:12:16:1216"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP server closed\u001b[39m","timestamp":"2025-06-03 02:12:16:1216"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mMongoDB disconnected\u001b[39m","timestamp":"2025-06-03 02:12:16:1216"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections closed\u001b[39m","timestamp":"2025-06-03 02:12:16:1216"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connected successfully\u001b[39m","timestamp":"2025-06-03 02:12:22:1222"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-06-03 02:12:22:1222"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTimescaleDB connected successfully\u001b[39m","timestamp":"2025-06-03 02:12:22:1222"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mElasticsearch connected successfully\u001b[39m","timestamp":"2025-06-03 02:12:23:1223"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m4 databases connected successfully\u001b[39m","timestamp":"2025-06-03 02:12:23:1223"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections established\u001b[39m","timestamp":"2025-06-03 02:12:23:1223"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI documentation enabled at /api/docs\u001b[39m","timestamp":"2025-06-03 02:12:23:1223"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExLog API server running on port 5000\u001b[39m","timestamp":"2025-06-03 02:12:23:1223"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-06-03 02:12:23:1223"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 02:12:42:1242"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 02:12:49:1249"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 02:13:31:1331"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 02:15:10:1510"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 02:15:39:1539"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 02:16:36:1636"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 02:16:47:1647"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM, shutting down WebSocket server...\u001b[39m","timestamp":"2025-06-03 02:17:09:179"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM. Starting graceful shutdown...\u001b[39m","timestamp":"2025-06-03 02:17:10:1710"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP server closed\u001b[39m","timestamp":"2025-06-03 02:17:10:1710"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mMongoDB disconnected\u001b[39m","timestamp":"2025-06-03 02:17:10:1710"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections closed\u001b[39m","timestamp":"2025-06-03 02:17:10:1710"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mWebSocket server started on port 5001\u001b[39m","timestamp":"2025-06-03 02:28:41:2841"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connected successfully\u001b[39m","timestamp":"2025-06-03 02:28:52:2852"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-06-03 02:28:52:2852"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTimescaleDB connected successfully\u001b[39m","timestamp":"2025-06-03 02:28:53:2853"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to Elasticsearch: connect ECONNREFUSED **********:9200\u001b[39m","meta":{"headers":{},"meta":{"aborted":false,"attempts":3,"connection":{"_openRequests":0,"deadCount":0,"headers":{"user-agent":"elasticsearch-js/8.18.2 (linux **********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"id":"http://elasticsearch:9200/","maxEventListeners":100,"pool":{"_events":{},"_eventsCount":1},"resurrectTimeout":0,"timeout":30000,"tls":null,"url":"http://elasticsearch:9200/","weight":1000},"context":null,"name":"elasticsearch-js","request":{"id":1,"options":{},"params":{"headers":{"accept":"application/vnd.elasticsearch+json; compatible-with=8,text/plain","user-agent":"elasticsearch-js/8.18.2 (linux **********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"method":"HEAD","path":"/","querystring":""}}},"statusCode":0,"warnings":null},"name":"ConnectionError","options":{"redaction":{"additionalKeys":[],"type":"replace"}},"stack":"ConnectionError: connect ECONNREFUSED **********:9200\n    at SniffingTransport._request (/app/node_modules/@elastic/transport/lib/Transport.js:595:31)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/node_modules/@elastic/transport/lib/Transport.js:627:32\n    at async SniffingTransport.request (/app/node_modules/@elastic/transport/lib/Transport.js:623:20)\n    at async Client.PingApi [as ping] (/app/node_modules/@elastic/elasticsearch/lib/api/api/ping.js:41:12)\n    at async DatabaseManager.connectElasticsearch (/app/src/config/database.js:73:7)\n    at async Promise.allSettled (index 2)\n    at async DatabaseManager.connectAll (/app/src/config/database.js:109:21)\n    at async ExLogServer.connectDatabases (/app/src/index.js:135:7)\n    at async ExLogServer.start (/app/src/index.js:146:7)","timestamp":"2025-06-03 02:29:01:291"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m3 databases connected successfully\u001b[39m","timestamp":"2025-06-03 02:29:01:291"}
{"0":"connect ECONNREFUSED **********:9200","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m1 databases failed to connect:\u001b[39m","timestamp":"2025-06-03 02:29:01:291"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections established\u001b[39m","timestamp":"2025-06-03 02:29:01:291"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI documentation enabled at /api/docs\u001b[39m","timestamp":"2025-06-03 02:29:01:291"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExLog API server running on port 5000\u001b[39m","timestamp":"2025-06-03 02:29:01:291"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-06-03 02:29:01:291"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 02:29:18:2918"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 02:30:25:3025"}
{"code":11000,"errorLabelSet":{},"errorResponse":{"code":11000,"errmsg":"E11000 duplicate key error collection: exlog.logs index: logId_1 dup key: { logId: \"connection-test-001\" }","index":0,"keyPattern":{"logId":1},"keyValue":{"logId":"connection-test-001"}},"index":0,"keyPattern":{"logId":1},"keyValue":{"logId":"connection-test-001"},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to process log connection-test-001: E11000 duplicate key error collection: exlog.logs index: logId_1 dup key: { logId: \"connection-test-001\" }\u001b[39m","stack":"MongoServerError: E11000 duplicate key error collection: exlog.logs index: logId_1 dup key: { logId: \"connection-test-001\" }\n    at InsertOneOperation.execute (/app/node_modules/mongodb/lib/operations/insert.js:51:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async tryOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:207:20)\n    at async executeOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:75:16)\n    at async Collection.insertOne (/app/node_modules/mongodb/lib/collection.js:157:16)","timestamp":"2025-06-03 02:30:51:3051"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 0 logs, failed 1 logs from agent 683e56c56d7889b16ed861e0\u001b[39m","timestamp":"2025-06-03 02:30:51:3051"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-03 02:30:51:3051","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683e56c56d7889b16ed861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683e56c56d7889b16ed861e0\u001b[39m","timestamp":"2025-06-03 02:30:55:3055"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683e56c56d7889b16ed861e0\u001b[39m","timestamp":"2025-06-03 02:30:55:3055"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683e56c56d7889b16ed861e0\u001b[39m","timestamp":"2025-06-03 02:30:55:3055"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683e56c56d7889b16ed861e0\u001b[39m","timestamp":"2025-06-03 02:30:55:3055"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683e56c56d7889b16ed861e0\u001b[39m","timestamp":"2025-06-03 02:31:02:312"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683e56c56d7889b16ed861e0\u001b[39m","timestamp":"2025-06-03 02:31:02:312"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683e56c56d7889b16ed861e0\u001b[39m","timestamp":"2025-06-03 02:31:02:312"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683e56c56d7889b16ed861e0\u001b[39m","timestamp":"2025-06-03 02:31:02:312"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683e56c56d7889b16ed861e0\u001b[39m","timestamp":"2025-06-03 02:31:07:317"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683e56c56d7889b16ed861e0\u001b[39m","timestamp":"2025-06-03 02:31:07:317"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683e56c56d7889b16ed861e0\u001b[39m","timestamp":"2025-06-03 02:31:07:317"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683e56c56d7889b16ed861e0\u001b[39m","timestamp":"2025-06-03 02:31:07:317"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683e56c56d7889b16ed861e0\u001b[39m","timestamp":"2025-06-03 02:31:07:317"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 02:31:08:318"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683e56c56d7889b16ed861e0\u001b[39m","timestamp":"2025-06-03 02:31:12:3112"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683e56c56d7889b16ed861e0\u001b[39m","timestamp":"2025-06-03 02:31:12:3112"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683e56c56d7889b16ed861e0\u001b[39m","timestamp":"2025-06-03 02:31:12:3112"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683e56c56d7889b16ed861e0\u001b[39m","timestamp":"2025-06-03 02:31:12:3112"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683e56c56d7889b16ed861e0\u001b[39m","timestamp":"2025-06-03 02:31:12:3112"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 02:32:24:3224"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM, shutting down WebSocket server...\u001b[39m","timestamp":"2025-06-03 02:42:29:4229"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM. Starting graceful shutdown...\u001b[39m","timestamp":"2025-06-03 02:42:30:4230"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP server closed\u001b[39m","timestamp":"2025-06-03 02:42:30:4230"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mMongoDB disconnected\u001b[39m","timestamp":"2025-06-03 02:42:30:4230"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections closed\u001b[39m","timestamp":"2025-06-03 02:42:30:4230"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mWebSocket server started on port 5001\u001b[39m","timestamp":"2025-06-03 02:47:51:4751"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connected successfully\u001b[39m","timestamp":"2025-06-03 02:47:56:4756"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-06-03 02:47:56:4756"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTimescaleDB connected successfully\u001b[39m","timestamp":"2025-06-03 02:47:56:4756"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to Elasticsearch: connect ECONNREFUSED **********:9200\u001b[39m","meta":{"headers":{},"meta":{"aborted":false,"attempts":3,"connection":{"_openRequests":0,"deadCount":0,"headers":{"user-agent":"elasticsearch-js/8.18.2 (linux **********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"id":"http://elasticsearch:9200/","maxEventListeners":100,"pool":{"_events":{},"_eventsCount":1},"resurrectTimeout":0,"timeout":30000,"tls":null,"url":"http://elasticsearch:9200/","weight":1000},"context":null,"name":"elasticsearch-js","request":{"id":1,"options":{},"params":{"headers":{"accept":"application/vnd.elasticsearch+json; compatible-with=8,text/plain","user-agent":"elasticsearch-js/8.18.2 (linux **********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"method":"HEAD","path":"/","querystring":""}}},"statusCode":0,"warnings":null},"name":"ConnectionError","options":{"redaction":{"additionalKeys":[],"type":"replace"}},"stack":"ConnectionError: connect ECONNREFUSED **********:9200\n    at SniffingTransport._request (/app/node_modules/@elastic/transport/lib/Transport.js:595:31)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/node_modules/@elastic/transport/lib/Transport.js:627:32\n    at async SniffingTransport.request (/app/node_modules/@elastic/transport/lib/Transport.js:623:20)\n    at async Client.PingApi [as ping] (/app/node_modules/@elastic/elasticsearch/lib/api/api/ping.js:41:12)\n    at async DatabaseManager.connectElasticsearch (/app/src/config/database.js:73:7)\n    at async Promise.allSettled (index 2)\n    at async DatabaseManager.connectAll (/app/src/config/database.js:109:21)\n    at async ExLogServer.connectDatabases (/app/src/index.js:135:7)\n    at async ExLogServer.start (/app/src/index.js:146:7)","timestamp":"2025-06-03 02:48:03:483"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m3 databases connected successfully\u001b[39m","timestamp":"2025-06-03 02:48:03:483"}
{"0":"connect ECONNREFUSED **********:9200","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m1 databases failed to connect:\u001b[39m","timestamp":"2025-06-03 02:48:03:483"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections established\u001b[39m","timestamp":"2025-06-03 02:48:03:483"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI documentation enabled at /api/docs\u001b[39m","timestamp":"2025-06-03 02:48:03:483"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExLog API server running on port 5000\u001b[39m","timestamp":"2025-06-03 02:48:03:483"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-06-03 02:48:03:483"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM. Starting graceful shutdown...\u001b[39m","timestamp":"2025-06-03 02:54:04:544"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP server closed\u001b[39m","timestamp":"2025-06-03 02:54:04:544"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mMongoDB disconnected\u001b[39m","timestamp":"2025-06-03 02:54:04:544"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections closed\u001b[39m","timestamp":"2025-06-03 02:54:04:544"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connected successfully\u001b[39m","timestamp":"2025-06-03 02:54:11:5411"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-06-03 02:54:11:5411"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTimescaleDB connected successfully\u001b[39m","timestamp":"2025-06-03 02:54:12:5412"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mElasticsearch connected successfully\u001b[39m","timestamp":"2025-06-03 02:54:12:5412"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m4 databases connected successfully\u001b[39m","timestamp":"2025-06-03 02:54:12:5412"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections established\u001b[39m","timestamp":"2025-06-03 02:54:12:5412"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI documentation enabled at /api/docs\u001b[39m","timestamp":"2025-06-03 02:54:12:5412"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExLog API server running on port 5000\u001b[39m","timestamp":"2025-06-03 02:54:12:5412"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-06-03 02:54:12:5412"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 02:55:07:557"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 02:55:08:558"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 02:55:15:5515"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM, shutting down WebSocket server...\u001b[39m","timestamp":"2025-06-03 02:55:45:5545"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM. Starting graceful shutdown...\u001b[39m","timestamp":"2025-06-03 02:55:46:5546"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP server closed\u001b[39m","timestamp":"2025-06-03 02:55:46:5546"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mMongoDB disconnected\u001b[39m","timestamp":"2025-06-03 02:55:46:5546"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections closed\u001b[39m","timestamp":"2025-06-03 02:55:46:5546"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mWebSocket server started on port 5001\u001b[39m","timestamp":"2025-06-03 02:59:33:5933"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connected successfully\u001b[39m","timestamp":"2025-06-03 02:59:43:5943"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-06-03 02:59:43:5943"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTimescaleDB connected successfully\u001b[39m","timestamp":"2025-06-03 02:59:44:5944"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to Elasticsearch: connect ECONNREFUSED **********:9200\u001b[39m","meta":{"headers":{},"meta":{"aborted":false,"attempts":3,"connection":{"_openRequests":0,"deadCount":0,"headers":{"user-agent":"elasticsearch-js/8.18.2 (linux **********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"id":"http://elasticsearch:9200/","maxEventListeners":100,"pool":{"_events":{},"_eventsCount":1},"resurrectTimeout":0,"timeout":30000,"tls":null,"url":"http://elasticsearch:9200/","weight":1000},"context":null,"name":"elasticsearch-js","request":{"id":1,"options":{},"params":{"headers":{"accept":"application/vnd.elasticsearch+json; compatible-with=8,text/plain","user-agent":"elasticsearch-js/8.18.2 (linux **********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"method":"HEAD","path":"/","querystring":""}}},"statusCode":0,"warnings":null},"name":"ConnectionError","options":{"redaction":{"additionalKeys":[],"type":"replace"}},"stack":"ConnectionError: connect ECONNREFUSED **********:9200\n    at SniffingTransport._request (/app/node_modules/@elastic/transport/lib/Transport.js:595:31)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/node_modules/@elastic/transport/lib/Transport.js:627:32\n    at async SniffingTransport.request (/app/node_modules/@elastic/transport/lib/Transport.js:623:20)\n    at async Client.PingApi [as ping] (/app/node_modules/@elastic/elasticsearch/lib/api/api/ping.js:41:12)\n    at async DatabaseManager.connectElasticsearch (/app/src/config/database.js:73:7)\n    at async Promise.allSettled (index 2)\n    at async DatabaseManager.connectAll (/app/src/config/database.js:109:21)\n    at async ExLogServer.connectDatabases (/app/src/index.js:135:7)\n    at async ExLogServer.start (/app/src/index.js:146:7)","timestamp":"2025-06-03 02:59:53:5953"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m3 databases connected successfully\u001b[39m","timestamp":"2025-06-03 02:59:53:5953"}
{"0":"connect ECONNREFUSED **********:9200","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m1 databases failed to connect:\u001b[39m","timestamp":"2025-06-03 02:59:53:5953"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections established\u001b[39m","timestamp":"2025-06-03 02:59:53:5953"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI documentation enabled at /api/docs\u001b[39m","timestamp":"2025-06-03 02:59:53:5953"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExLog API server running on port 5000\u001b[39m","timestamp":"2025-06-03 02:59:53:5953"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-06-03 02:59:53:5953"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 03:01:30:130"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 03:03:48:348"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 03:03:49:349"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged out: <EMAIL>\u001b[39m","timestamp":"2025-06-03 03:03:49:349"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged out: <EMAIL>\u001b[39m","timestamp":"2025-06-03 03:15:13:1513"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 03:15:16:1516"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM, shutting down WebSocket server...\u001b[39m","timestamp":"2025-06-03 03:39:27:3927"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM. Starting graceful shutdown...\u001b[39m","timestamp":"2025-06-03 03:39:28:3928"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP server closed\u001b[39m","timestamp":"2025-06-03 03:39:28:3928"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mMongoDB disconnected\u001b[39m","timestamp":"2025-06-03 03:39:28:3928"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections closed\u001b[39m","timestamp":"2025-06-03 03:39:28:3928"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mWebSocket server started on port 5001\u001b[39m","timestamp":"2025-06-03 03:44:38:4438"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connected successfully\u001b[39m","timestamp":"2025-06-03 03:48:08:488"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-06-03 03:48:08:488"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mElasticsearch connected successfully\u001b[39m","timestamp":"2025-06-03 03:48:09:489"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTimescaleDB connected successfully\u001b[39m","timestamp":"2025-06-03 03:48:09:489"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m4 databases connected successfully\u001b[39m","timestamp":"2025-06-03 03:48:09:489"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections established\u001b[39m","timestamp":"2025-06-03 03:48:09:489"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI documentation enabled at /api/docs\u001b[39m","timestamp":"2025-06-03 03:48:09:489"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExLog API server running on port 5000\u001b[39m","timestamp":"2025-06-03 03:48:09:489"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-06-03 03:48:09:489"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 03:48:32:4832"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged out: <EMAIL>\u001b[39m","timestamp":"2025-06-03 03:50:34:5034"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 03:50:36:5036"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mPassword changed for user: <EMAIL>\u001b[39m","timestamp":"2025-06-03 03:55:48:5548","userId":"683e56c56d7889b16ed861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged out: <EMAIL>\u001b[39m","timestamp":"2025-06-03 03:56:01:561"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 03:56:06:566"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged out: <EMAIL>\u001b[39m","timestamp":"2025-06-03 03:56:09:569"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Invalid email or password\u001b[39m","method":"POST","stack":"Error: Invalid email or password\n    at /app/src/routes/auth.js:203:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-03 03:56:17:5617","url":"/api/v1/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 03:56:24:5624"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM, shutting down WebSocket server...\u001b[39m","timestamp":"2025-06-03 04:10:28:1028"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM. Starting graceful shutdown...\u001b[39m","timestamp":"2025-06-03 04:10:30:1030"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP server closed\u001b[39m","timestamp":"2025-06-03 04:10:30:1030"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mMongoDB disconnected\u001b[39m","timestamp":"2025-06-03 04:10:30:1030"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections closed\u001b[39m","timestamp":"2025-06-03 04:10:30:1030"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mWebSocket server started on port 5001\u001b[39m","timestamp":"2025-06-03 04:14:49:1449"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connected successfully\u001b[39m","timestamp":"2025-06-03 04:14:56:1456"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-06-03 04:14:56:1456"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTimescaleDB connected successfully\u001b[39m","timestamp":"2025-06-03 04:14:56:1456"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to Elasticsearch: connect ECONNREFUSED **********:9200\u001b[39m","meta":{"headers":{},"meta":{"aborted":false,"attempts":3,"connection":{"_openRequests":0,"deadCount":0,"headers":{"user-agent":"elasticsearch-js/8.18.2 (linux **********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"id":"http://elasticsearch:9200/","maxEventListeners":100,"pool":{"_events":{},"_eventsCount":1},"resurrectTimeout":0,"timeout":30000,"tls":null,"url":"http://elasticsearch:9200/","weight":1000},"context":null,"name":"elasticsearch-js","request":{"id":1,"options":{},"params":{"headers":{"accept":"application/vnd.elasticsearch+json; compatible-with=8,text/plain","user-agent":"elasticsearch-js/8.18.2 (linux **********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"method":"HEAD","path":"/","querystring":""}}},"statusCode":0,"warnings":null},"name":"ConnectionError","options":{"redaction":{"additionalKeys":[],"type":"replace"}},"stack":"ConnectionError: connect ECONNREFUSED **********:9200\n    at SniffingTransport._request (/app/node_modules/@elastic/transport/lib/Transport.js:595:31)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/node_modules/@elastic/transport/lib/Transport.js:627:32\n    at async SniffingTransport.request (/app/node_modules/@elastic/transport/lib/Transport.js:623:20)\n    at async Client.PingApi [as ping] (/app/node_modules/@elastic/elasticsearch/lib/api/api/ping.js:41:12)\n    at async DatabaseManager.connectElasticsearch (/app/src/config/database.js:73:7)\n    at async Promise.allSettled (index 2)\n    at async DatabaseManager.connectAll (/app/src/config/database.js:109:21)\n    at async ExLogServer.connectDatabases (/app/src/index.js:139:7)\n    at async ExLogServer.start (/app/src/index.js:150:7)","timestamp":"2025-06-03 04:15:06:156"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m3 databases connected successfully\u001b[39m","timestamp":"2025-06-03 04:15:06:156"}
{"0":"connect ECONNREFUSED **********:9200","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m1 databases failed to connect:\u001b[39m","timestamp":"2025-06-03 04:15:06:156"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections established\u001b[39m","timestamp":"2025-06-03 04:15:06:156"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI documentation enabled at /api/docs\u001b[39m","timestamp":"2025-06-03 04:15:06:156"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExLog API server running on port 5000\u001b[39m","timestamp":"2025-06-03 04:15:06:156"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-06-03 04:15:06:156"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer accessible on all network interfaces\u001b[39m","timestamp":"2025-06-03 04:15:06:156"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged out: <EMAIL>\u001b[39m","timestamp":"2025-06-03 04:17:10:1710"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 04:17:23:1723"}
{"code":11000,"errorLabelSet":{},"errorResponse":{"code":11000,"errmsg":"E11000 duplicate key error collection: exlog.logs index: logId_1 dup key: { logId: \"connection-test-001\" }","index":0,"keyPattern":{"logId":1},"keyValue":{"logId":"connection-test-001"}},"index":0,"keyPattern":{"logId":1},"keyValue":{"logId":"connection-test-001"},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to process log connection-test-001: E11000 duplicate key error collection: exlog.logs index: logId_1 dup key: { logId: \"connection-test-001\" }\u001b[39m","stack":"MongoServerError: E11000 duplicate key error collection: exlog.logs index: logId_1 dup key: { logId: \"connection-test-001\" }\n    at InsertOneOperation.execute (/app/node_modules/mongodb/lib/operations/insert.js:51:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async tryOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:207:20)\n    at async executeOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:75:16)\n    at async Collection.insertOne (/app/node_modules/mongodb/lib/collection.js:157:16)","timestamp":"2025-06-03 04:19:44:1944"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 0 logs, failed 1 logs from agent 683e56c56d7889b16ed861e0\u001b[39m","timestamp":"2025-06-03 04:19:44:1944"}
{"ip":"**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-03 04:19:44:1944","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683e56c56d7889b16ed861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683e56c56d7889b16ed861e0\u001b[39m","timestamp":"2025-06-03 04:19:48:1948"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683e56c56d7889b16ed861e0\u001b[39m","timestamp":"2025-06-03 04:19:48:1948"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683e56c56d7889b16ed861e0\u001b[39m","timestamp":"2025-06-03 04:19:48:1948"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683e56c56d7889b16ed861e0\u001b[39m","timestamp":"2025-06-03 04:19:49:1949"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683e56c56d7889b16ed861e0\u001b[39m","timestamp":"2025-06-03 04:19:53:1953"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683e56c56d7889b16ed861e0\u001b[39m","timestamp":"2025-06-03 04:19:53:1953"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683e56c56d7889b16ed861e0\u001b[39m","timestamp":"2025-06-03 04:19:53:1953"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683e56c56d7889b16ed861e0\u001b[39m","timestamp":"2025-06-03 04:19:53:1953"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683e56c56d7889b16ed861e0\u001b[39m","timestamp":"2025-06-03 04:19:53:1953"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683e56c56d7889b16ed861e0\u001b[39m","timestamp":"2025-06-03 04:19:53:1953"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mWebSocket server started on port 5001\u001b[39m","timestamp":"2025-06-03 15:08:11:811"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connected successfully\u001b[39m","timestamp":"2025-06-03 15:08:38:838"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-06-03 15:08:42:842"}
{"code":"EAI_AGAIN","errno":-3001,"hostname":"timescaledb","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to TimescaleDB: getaddrinfo EAI_AGAIN timescaledb\u001b[39m","stack":"Error: getaddrinfo EAI_AGAIN timescaledb\n    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:107:26)","syscall":"getaddrinfo","timestamp":"2025-06-03 15:08:45:845"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to Elasticsearch: connect ECONNREFUSED **********:9200\u001b[39m","meta":{"headers":{},"meta":{"aborted":false,"attempts":3,"connection":{"_openRequests":0,"deadCount":0,"headers":{"user-agent":"elasticsearch-js/8.18.2 (linux **********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"id":"http://elasticsearch:9200/","maxEventListeners":100,"pool":{"_events":{},"_eventsCount":1},"resurrectTimeout":0,"timeout":30000,"tls":null,"url":"http://elasticsearch:9200/","weight":1000},"context":null,"name":"elasticsearch-js","request":{"id":1,"options":{},"params":{"headers":{"accept":"application/vnd.elasticsearch+json; compatible-with=8,text/plain","user-agent":"elasticsearch-js/8.18.2 (linux **********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"method":"HEAD","path":"/","querystring":""}}},"statusCode":0,"warnings":null},"name":"ConnectionError","options":{"redaction":{"additionalKeys":[],"type":"replace"}},"stack":"ConnectionError: connect ECONNREFUSED **********:9200\n    at SniffingTransport._request (/app/node_modules/@elastic/transport/lib/Transport.js:595:31)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/node_modules/@elastic/transport/lib/Transport.js:627:32\n    at async SniffingTransport.request (/app/node_modules/@elastic/transport/lib/Transport.js:623:20)\n    at async Client.PingApi [as ping] (/app/node_modules/@elastic/elasticsearch/lib/api/api/ping.js:41:12)\n    at async DatabaseManager.connectElasticsearch (/app/src/config/database.js:73:7)\n    at async Promise.allSettled (index 2)\n    at async DatabaseManager.connectAll (/app/src/config/database.js:109:21)\n    at async ExLogServer.connectDatabases (/app/src/index.js:139:7)\n    at async ExLogServer.start (/app/src/index.js:150:7)","timestamp":"2025-06-03 15:08:47:847"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m2 databases connected successfully\u001b[39m","timestamp":"2025-06-03 15:08:47:847"}
{"0":"getaddrinfo EAI_AGAIN timescaledb","1":"connect ECONNREFUSED **********:9200","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m2 databases failed to connect:\u001b[39m","timestamp":"2025-06-03 15:08:47:847"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections established\u001b[39m","timestamp":"2025-06-03 15:08:47:847"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI documentation enabled at /api/docs\u001b[39m","timestamp":"2025-06-03 15:08:47:847"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExLog API server running on port 5000\u001b[39m","timestamp":"2025-06-03 15:08:47:847"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-06-03 15:08:47:847"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer accessible on all network interfaces\u001b[39m","timestamp":"2025-06-03 15:08:47:847"}
{"ip":"**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Invalid email or password\u001b[39m","method":"POST","stack":"Error: Invalid email or password\n    at /app/src/routes/auth.js:203:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-03 15:12:45:1245","url":"/api/v1/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 15:13:04:134"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mNew API key created: testkey\u001b[39m","timestamp":"2025-06-03 15:14:16:1416","userId":"683f0ff482d5e00e51d861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 1 logs, failed 0 logs from agent 683f0ff482d5e00e51d861e0\u001b[39m","timestamp":"2025-06-03 15:15:53:1553"}
{"ip":"**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-03 15:15:53:1553","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683f0ff482d5e00e51d861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683f0ff482d5e00e51d861e0\u001b[39m","timestamp":"2025-06-03 15:15:58:1558"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683f0ff482d5e00e51d861e0\u001b[39m","timestamp":"2025-06-03 15:15:58:1558"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683f0ff482d5e00e51d861e0\u001b[39m","timestamp":"2025-06-03 15:15:58:1558"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683f0ff482d5e00e51d861e0\u001b[39m","timestamp":"2025-06-03 15:15:58:1558"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683f0ff482d5e00e51d861e0\u001b[39m","timestamp":"2025-06-03 15:16:02:162"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683f0ff482d5e00e51d861e0\u001b[39m","timestamp":"2025-06-03 15:16:02:162"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683f0ff482d5e00e51d861e0\u001b[39m","timestamp":"2025-06-03 15:16:02:162"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683f0ff482d5e00e51d861e0\u001b[39m","timestamp":"2025-06-03 15:16:02:162"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683f0ff482d5e00e51d861e0\u001b[39m","timestamp":"2025-06-03 15:16:02:162"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM. Starting graceful shutdown...\u001b[39m","timestamp":"2025-06-03 15:44:54:4454"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP server closed\u001b[39m","timestamp":"2025-06-03 15:44:54:4454"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mMongoDB disconnected\u001b[39m","timestamp":"2025-06-03 15:44:54:4454"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections closed\u001b[39m","timestamp":"2025-06-03 15:44:54:4454"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connected successfully\u001b[39m","timestamp":"2025-06-03 15:45:01:451"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-06-03 15:45:01:451"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTimescaleDB connected successfully\u001b[39m","timestamp":"2025-06-03 15:45:01:451"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mElasticsearch connected successfully\u001b[39m","timestamp":"2025-06-03 15:45:01:451"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m4 databases connected successfully\u001b[39m","timestamp":"2025-06-03 15:45:01:451"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections established\u001b[39m","timestamp":"2025-06-03 15:45:01:451"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI documentation enabled at /api/docs\u001b[39m","timestamp":"2025-06-03 15:45:01:451"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExLog API server running on port 5000\u001b[39m","timestamp":"2025-06-03 15:45:01:451"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-06-03 15:45:01:451"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer accessible on all network interfaces\u001b[39m","timestamp":"2025-06-03 15:45:01:451"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 15:48:46:4846"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM. Starting graceful shutdown...\u001b[39m","timestamp":"2025-06-03 15:49:47:4947"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP server closed\u001b[39m","timestamp":"2025-06-03 15:49:47:4947"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mMongoDB disconnected\u001b[39m","timestamp":"2025-06-03 15:49:47:4947"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM, shutting down WebSocket server...\u001b[39m","timestamp":"2025-06-03 15:49:47:4947"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections closed\u001b[39m","timestamp":"2025-06-03 15:49:47:4947"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mWebSocket server started on port 5001\u001b[39m","timestamp":"2025-06-03 15:49:51:4951"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connected successfully\u001b[39m","timestamp":"2025-06-03 15:49:54:4954"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-06-03 15:49:54:4954"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTimescaleDB connected successfully\u001b[39m","timestamp":"2025-06-03 15:49:54:4954"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to Elasticsearch: connect ECONNREFUSED **********:9200\u001b[39m","meta":{"headers":{},"meta":{"aborted":false,"attempts":3,"connection":{"_openRequests":0,"deadCount":0,"headers":{"user-agent":"elasticsearch-js/8.18.2 (linux **********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"id":"http://elasticsearch:9200/","maxEventListeners":100,"pool":{"_events":{},"_eventsCount":1},"resurrectTimeout":0,"timeout":30000,"tls":null,"url":"http://elasticsearch:9200/","weight":1000},"context":null,"name":"elasticsearch-js","request":{"id":1,"options":{},"params":{"headers":{"accept":"application/vnd.elasticsearch+json; compatible-with=8,text/plain","user-agent":"elasticsearch-js/8.18.2 (linux **********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"method":"HEAD","path":"/","querystring":""}}},"statusCode":0,"warnings":null},"name":"ConnectionError","options":{"redaction":{"additionalKeys":[],"type":"replace"}},"stack":"ConnectionError: connect ECONNREFUSED **********:9200\n    at SniffingTransport._request (/app/node_modules/@elastic/transport/lib/Transport.js:595:31)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/node_modules/@elastic/transport/lib/Transport.js:627:32\n    at async SniffingTransport.request (/app/node_modules/@elastic/transport/lib/Transport.js:623:20)\n    at async Client.PingApi [as ping] (/app/node_modules/@elastic/elasticsearch/lib/api/api/ping.js:41:12)\n    at async DatabaseManager.connectElasticsearch (/app/src/config/database.js:73:7)\n    at async Promise.allSettled (index 2)\n    at async DatabaseManager.connectAll (/app/src/config/database.js:109:21)\n    at async ExLogServer.connectDatabases (/app/src/index.js:139:7)\n    at async ExLogServer.start (/app/src/index.js:150:7)","timestamp":"2025-06-03 15:50:05:505"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m3 databases connected successfully\u001b[39m","timestamp":"2025-06-03 15:50:05:505"}
{"0":"connect ECONNREFUSED **********:9200","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m1 databases failed to connect:\u001b[39m","timestamp":"2025-06-03 15:50:05:505"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections established\u001b[39m","timestamp":"2025-06-03 15:50:05:505"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI documentation enabled at /api/docs\u001b[39m","timestamp":"2025-06-03 15:50:05:505"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExLog API server running on port 5000\u001b[39m","timestamp":"2025-06-03 15:50:05:505"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-06-03 15:50:05:505"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer accessible on all network interfaces\u001b[39m","timestamp":"2025-06-03 15:50:05:505"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM. Starting graceful shutdown...\u001b[39m","timestamp":"2025-06-03 15:58:33:5833"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP server closed\u001b[39m","timestamp":"2025-06-03 15:58:33:5833"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mMongoDB disconnected\u001b[39m","timestamp":"2025-06-03 15:58:33:5833"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections closed\u001b[39m","timestamp":"2025-06-03 15:58:33:5833"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connected successfully\u001b[39m","timestamp":"2025-06-03 15:58:35:5835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-06-03 15:58:35:5835"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTimescaleDB connected successfully\u001b[39m","timestamp":"2025-06-03 15:58:36:5836"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mElasticsearch connected successfully\u001b[39m","timestamp":"2025-06-03 15:58:36:5836"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m4 databases connected successfully\u001b[39m","timestamp":"2025-06-03 15:58:36:5836"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections established\u001b[39m","timestamp":"2025-06-03 15:58:36:5836"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI documentation enabled at /api/docs\u001b[39m","timestamp":"2025-06-03 15:58:36:5836"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExLog API server running on port 5000\u001b[39m","timestamp":"2025-06-03 15:58:36:5836"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-06-03 15:58:36:5836"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer accessible on all network interfaces\u001b[39m","timestamp":"2025-06-03 15:58:36:5836"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 16:32:36:3236"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 16:32:36:3236"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM. Starting graceful shutdown...\u001b[39m","timestamp":"2025-06-03 16:34:17:3417"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP server closed\u001b[39m","timestamp":"2025-06-03 16:34:17:3417"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mMongoDB disconnected\u001b[39m","timestamp":"2025-06-03 16:34:17:3417"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections closed\u001b[39m","timestamp":"2025-06-03 16:34:17:3417"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connected successfully\u001b[39m","timestamp":"2025-06-03 16:34:20:3420"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-06-03 16:34:21:3421"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mElasticsearch connected successfully\u001b[39m","timestamp":"2025-06-03 16:34:21:3421"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTimescaleDB connected successfully\u001b[39m","timestamp":"2025-06-03 16:34:21:3421"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m4 databases connected successfully\u001b[39m","timestamp":"2025-06-03 16:34:21:3421"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections established\u001b[39m","timestamp":"2025-06-03 16:34:21:3421"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI documentation enabled at /api/docs\u001b[39m","timestamp":"2025-06-03 16:34:21:3421"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExLog API server running on port 5000\u001b[39m","timestamp":"2025-06-03 16:34:21:3421"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-06-03 16:34:21:3421"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer accessible on all network interfaces\u001b[39m","timestamp":"2025-06-03 16:34:21:3421"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 16:34:26:3426"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 16:34:26:3426"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 16:34:27:3427"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 16:34:27:3427"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 16:34:27:3427"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 16:34:28:3428"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 16:34:28:3428"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 16:34:28:3428"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 16:34:29:3429"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 16:36:31:3631"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM, shutting down WebSocket server...\u001b[39m","timestamp":"2025-06-03 16:40:59:4059"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM. Starting graceful shutdown...\u001b[39m","timestamp":"2025-06-03 16:41:00:410"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP server closed\u001b[39m","timestamp":"2025-06-03 16:41:00:410"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mMongoDB disconnected\u001b[39m","timestamp":"2025-06-03 16:41:00:410"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections closed\u001b[39m","timestamp":"2025-06-03 16:41:00:410"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mWebSocket server started on port 5001\u001b[39m","timestamp":"2025-06-03 16:42:39:4239"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connected successfully\u001b[39m","timestamp":"2025-06-03 16:42:45:4245"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-06-03 16:42:45:4245"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTimescaleDB connected successfully\u001b[39m","timestamp":"2025-06-03 16:42:45:4245"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to Elasticsearch: connect ECONNREFUSED 172.18.0.3:9200\u001b[39m","meta":{"headers":{},"meta":{"aborted":false,"attempts":3,"connection":{"_openRequests":0,"deadCount":0,"headers":{"user-agent":"elasticsearch-js/8.18.2 (linux **********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"id":"http://elasticsearch:9200/","maxEventListeners":100,"pool":{"_events":{},"_eventsCount":1},"resurrectTimeout":0,"timeout":30000,"tls":null,"url":"http://elasticsearch:9200/","weight":1000},"context":null,"name":"elasticsearch-js","request":{"id":1,"options":{},"params":{"headers":{"accept":"application/vnd.elasticsearch+json; compatible-with=8,text/plain","user-agent":"elasticsearch-js/8.18.2 (linux **********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"method":"HEAD","path":"/","querystring":""}}},"statusCode":0,"warnings":null},"name":"ConnectionError","options":{"redaction":{"additionalKeys":[],"type":"replace"}},"stack":"ConnectionError: connect ECONNREFUSED 172.18.0.3:9200\n    at SniffingTransport._request (/app/node_modules/@elastic/transport/lib/Transport.js:595:31)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/node_modules/@elastic/transport/lib/Transport.js:627:32\n    at async SniffingTransport.request (/app/node_modules/@elastic/transport/lib/Transport.js:623:20)\n    at async Client.PingApi [as ping] (/app/node_modules/@elastic/elasticsearch/lib/api/api/ping.js:41:12)\n    at async DatabaseManager.connectElasticsearch (/app/src/config/database.js:73:7)\n    at async Promise.allSettled (index 2)\n    at async DatabaseManager.connectAll (/app/src/config/database.js:109:21)\n    at async ExLogServer.connectDatabases (/app/src/index.js:144:7)\n    at async ExLogServer.start (/app/src/index.js:155:7)","timestamp":"2025-06-03 16:42:53:4253"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m3 databases connected successfully\u001b[39m","timestamp":"2025-06-03 16:42:53:4253"}
{"0":"connect ECONNREFUSED 172.18.0.3:9200","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m1 databases failed to connect:\u001b[39m","timestamp":"2025-06-03 16:42:53:4253"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections established\u001b[39m","timestamp":"2025-06-03 16:42:53:4253"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI documentation enabled at /api/docs\u001b[39m","timestamp":"2025-06-03 16:42:53:4253"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExLog API server running on port 5000\u001b[39m","timestamp":"2025-06-03 16:42:53:4253"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-06-03 16:42:53:4253"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer accessible on all network interfaces\u001b[39m","timestamp":"2025-06-03 16:42:53:4253"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged out: <EMAIL>\u001b[39m","timestamp":"2025-06-03 16:50:44:5044"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 16:50:51:5051"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 16:55:15:5515"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 16:56:00:560"}
{"ip":"**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Cast to ObjectId failed for value \"profile\" (type string) at path \"_id\" for model \"User\"\u001b[39m","method":"GET","stack":"CastError: Cast to ObjectId failed for value \"profile\" (type string) at path \"_id\" for model \"User\"\n    at SchemaObjectId.cast (/app/node_modules/mongoose/lib/schema/objectId.js:251:11)\n    at SchemaType.applySetters (/app/node_modules/mongoose/lib/schemaType.js:1255:12)\n    at SchemaType.castForQuery (/app/node_modules/mongoose/lib/schemaType.js:1673:17)\n    at cast (/app/node_modules/mongoose/lib/cast.js:390:32)\n    at Query.cast (/app/node_modules/mongoose/lib/query.js:5055:12)\n    at Query._castConditions (/app/node_modules/mongoose/lib/query.js:2351:10)\n    at model.Query._findOne (/app/node_modules/mongoose/lib/query.js:2674:8)\n    at model.Query.exec (/app/node_modules/mongoose/lib/query.js:4604:80)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/src/routes/users.js:93:16","timestamp":"2025-06-03 16:56:00:560","url":"/api/v1/users/profile","userAgent":"axios/1.9.0","userId":"683f0ff482d5e00e51d861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 17:00:18:018"}
{"ip":"**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Cast to ObjectId failed for value \"profile\" (type string) at path \"_id\" for model \"User\"\u001b[39m","method":"GET","stack":"CastError: Cast to ObjectId failed for value \"profile\" (type string) at path \"_id\" for model \"User\"\n    at SchemaObjectId.cast (/app/node_modules/mongoose/lib/schema/objectId.js:251:11)\n    at SchemaType.applySetters (/app/node_modules/mongoose/lib/schemaType.js:1255:12)\n    at SchemaType.castForQuery (/app/node_modules/mongoose/lib/schemaType.js:1673:17)\n    at cast (/app/node_modules/mongoose/lib/cast.js:390:32)\n    at Query.cast (/app/node_modules/mongoose/lib/query.js:5055:12)\n    at Query._castConditions (/app/node_modules/mongoose/lib/query.js:2351:10)\n    at model.Query._findOne (/app/node_modules/mongoose/lib/query.js:2674:8)\n    at model.Query.exec (/app/node_modules/mongoose/lib/query.js:4604:80)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/src/routes/users.js:93:16","timestamp":"2025-06-03 17:00:18:018","url":"/api/v1/users/profile","userAgent":"axios/1.9.0","userId":"683f0ff482d5e00e51d861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 17:05:00:50"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 17:06:47:647"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-03 18:18:34:1834"}
{"code":11000,"errorLabelSet":{},"errorResponse":{"code":11000,"errmsg":"E11000 duplicate key error collection: exlog.logs index: logId_1 dup key: { logId: \"connection-test-001\" }","index":0,"keyPattern":{"logId":1},"keyValue":{"logId":"connection-test-001"}},"index":0,"keyPattern":{"logId":1},"keyValue":{"logId":"connection-test-001"},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to process log connection-test-001: E11000 duplicate key error collection: exlog.logs index: logId_1 dup key: { logId: \"connection-test-001\" }\u001b[39m","stack":"MongoServerError: E11000 duplicate key error collection: exlog.logs index: logId_1 dup key: { logId: \"connection-test-001\" }\n    at InsertOneOperation.execute (/app/node_modules/mongodb/lib/operations/insert.js:51:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async tryOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:207:20)\n    at async executeOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:75:16)\n    at async Collection.insertOne (/app/node_modules/mongodb/lib/collection.js:157:16)","timestamp":"2025-06-03 18:20:08:208"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 0 logs, failed 1 logs from agent 683f0ff482d5e00e51d861e0\u001b[39m","timestamp":"2025-06-03 18:20:08:208"}
{"ip":"**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-03 18:20:08:208","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683f0ff482d5e00e51d861e0"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683f0ff482d5e00e51d861e0\u001b[39m","timestamp":"2025-06-03 18:20:13:2013"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683f0ff482d5e00e51d861e0\u001b[39m","timestamp":"2025-06-03 18:20:13:2013"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683f0ff482d5e00e51d861e0\u001b[39m","timestamp":"2025-06-03 18:20:13:2013"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683f0ff482d5e00e51d861e0\u001b[39m","timestamp":"2025-06-03 18:20:13:2013"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683f0ff482d5e00e51d861e0\u001b[39m","timestamp":"2025-06-03 18:20:17:2017"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683f0ff482d5e00e51d861e0\u001b[39m","timestamp":"2025-06-03 18:20:17:2017"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683f0ff482d5e00e51d861e0\u001b[39m","timestamp":"2025-06-03 18:20:17:2017"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683f0ff482d5e00e51d861e0\u001b[39m","timestamp":"2025-06-03 18:20:17:2017"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683f0ff482d5e00e51d861e0\u001b[39m","timestamp":"2025-06-03 18:20:17:2017"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessed 10 logs, failed 0 logs from agent 683f0ff482d5e00e51d861e0\u001b[39m","timestamp":"2025-06-03 18:20:17:2017"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM, shutting down WebSocket server...\u001b[39m","timestamp":"2025-06-03 21:37:42:3742"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM. Starting graceful shutdown...\u001b[39m","timestamp":"2025-06-03 21:37:44:3744"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP server closed\u001b[39m","timestamp":"2025-06-03 21:37:44:3744"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mMongoDB disconnected\u001b[39m","timestamp":"2025-06-03 21:37:44:3744"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections closed\u001b[39m","timestamp":"2025-06-03 21:37:44:3744"}
