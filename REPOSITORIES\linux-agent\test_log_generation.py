#!/usr/bin/env python3
"""
Test script to generate sample logs and send them to the dashboard
to verify the Linux agent integration.
"""

import sys
import time
import random
from datetime import datetime, timedelta
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.config_manager import ConfigManager
from utils.api_client import ExLogAPIClient
from utils.logger import LoggerSetup


def generate_sample_logs(count=10):
    """Generate sample logs that simulate Linux system logs."""
    
    log_templates = [
        {
            'source': 'System',
            'sourceType': 'event',
            'logLevel': 'info',
            'message_template': 'System service {} started successfully',
            'services': ['nginx', 'apache2', 'mysql', 'postgresql', 'ssh']
        },
        {
            'source': 'Security',
            'sourceType': 'security',
            'logLevel': 'warning',
            'message_template': 'Failed login attempt for user {} from IP {}',
            'users': ['root', 'admin', 'user', 'guest'],
            'ips': ['*************', '*********', '***********', '************']
        },
        {
            'source': 'Application',
            'sourceType': 'application',
            'logLevel': 'error',
            'message_template': 'Application {} encountered error: {}',
            'apps': ['web-server', 'database', 'api-service', 'worker'],
            'errors': ['Connection timeout', 'Memory allocation failed', 'Invalid request', 'Resource not found']
        },
        {
            'source': 'Network',
            'sourceType': 'network',
            'logLevel': 'info',
            'message_template': 'Network interface {} is {}',
            'interfaces': ['eth0', 'eth1', 'wlan0', 'lo'],
            'states': ['up', 'down', 'connected', 'disconnected']
        },
        {
            'source': 'System',
            'sourceType': 'audit',
            'logLevel': 'info',
            'message_template': 'User {} executed command: {}',
            'users': ['root', 'admin', 'user'],
            'commands': ['ls -la', 'ps aux', 'netstat -an', 'tail -f /var/log/syslog']
        }
    ]
    
    logs = []
    base_time = datetime.now() - timedelta(hours=1)
    
    for i in range(count):
        template = random.choice(log_templates)
        
        # Generate timestamp (spread over the last hour)
        timestamp = base_time + timedelta(minutes=random.randint(0, 60))
        
        # Generate message based on template
        if 'services' in template:
            message = template['message_template'].format(random.choice(template['services']))
        elif 'users' in template and 'ips' in template:
            message = template['message_template'].format(
                random.choice(template['users']),
                random.choice(template['ips'])
            )
        elif 'apps' in template and 'errors' in template:
            message = template['message_template'].format(
                random.choice(template['apps']),
                random.choice(template['errors'])
            )
        elif 'interfaces' in template and 'states' in template:
            message = template['message_template'].format(
                random.choice(template['interfaces']),
                random.choice(template['states'])
            )
        elif 'users' in template and 'commands' in template:
            message = template['message_template'].format(
                random.choice(template['users']),
                random.choice(template['commands'])
            )
        else:
            message = template['message_template']
        
        log_entry = {
            'logId': f'linux-agent-test-{i+1:03d}-{int(timestamp.timestamp())}',
            'timestamp': timestamp.isoformat() + 'Z',
            'source': template['source'],
            'sourceType': template['sourceType'],
            'host': 'linux-test-server',
            'logLevel': template['logLevel'],
            'message': message,
            'additionalFields': {
                'agent_type': 'linux',
                'test_run': True,
                'generated_at': datetime.now().isoformat()
            },
            'tags': ['linux-agent', 'test', template['sourceType']],
            'severity': {'info': 2, 'warning': 3, 'error': 4, 'critical': 5}.get(template['logLevel'], 2)
        }
        
        logs.append(log_entry)
    
    return logs


def main():
    """Main test function."""
    print("🐧 Linux Agent Log Generation Test")
    print("=" * 50)
    
    # Set up logging
    LoggerSetup.setup_logging(log_level='INFO', console_output=True)
    
    try:
        # Load configuration
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        # Initialize API client
        api_config = config.get('api', {})
        if not api_config.get('enabled', False):
            print("❌ API integration is disabled in configuration")
            return 1
        
        client = ExLogAPIClient(api_config)
        
        # Test connection first
        print("1️⃣ Testing API connection...")
        if not client.test_connection():
            print("❌ API connection test failed")
            return 1
        print("✅ API connection successful")
        
        # Generate sample logs
        print("\n2️⃣ Generating sample logs...")
        sample_logs = generate_sample_logs(15)
        print(f"✅ Generated {len(sample_logs)} sample logs")
        
        # Display sample of what we're sending
        print("\n📋 Sample logs to be sent:")
        for i, log in enumerate(sample_logs[:3]):
            print(f"   {i+1}. [{log['logLevel'].upper()}] {log['source']}: {log['message']}")
        if len(sample_logs) > 3:
            print(f"   ... and {len(sample_logs) - 3} more logs")
        
        # Send logs to dashboard
        print(f"\n3️⃣ Sending {len(sample_logs)} logs to dashboard...")
        success = client.send_logs(sample_logs)
        
        if success:
            print("✅ Logs sent successfully!")
            print(f"📊 Statistics: {client.get_statistics()}")
        else:
            print("❌ Failed to send logs")
            return 1
        
        # Wait a moment and check if logs appear in dashboard
        print("\n4️⃣ Verification complete!")
        print("🔍 Check the dashboard at http://localhost:3000 to see the logs")
        print("📝 Look for logs from host 'linux-test-server' with tags 'linux-agent' and 'test'")
        
        return 0
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
