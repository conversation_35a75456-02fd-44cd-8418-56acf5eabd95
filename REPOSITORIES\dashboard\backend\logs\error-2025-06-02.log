{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to Elasticsearch: connect ECONNREFUSED **********:9200\u001b[39m","meta":{"headers":{},"meta":{"aborted":false,"attempts":3,"connection":{"_openRequests":0,"deadCount":0,"headers":{"user-agent":"elasticsearch-js/8.18.2 (linux **********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"id":"http://elasticsearch:9200/","maxEventListeners":100,"pool":{"_events":{},"_eventsCount":1},"resurrectTimeout":0,"timeout":30000,"tls":null,"url":"http://elasticsearch:9200/","weight":1000},"context":null,"name":"elasticsearch-js","request":{"id":1,"options":{},"params":{"headers":{"accept":"application/vnd.elasticsearch+json; compatible-with=8,text/plain","user-agent":"elasticsearch-js/8.18.2 (linux **********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"method":"HEAD","path":"/","querystring":""}}},"statusCode":0,"warnings":null},"name":"ConnectionError","options":{"redaction":{"additionalKeys":[],"type":"replace"}},"stack":"ConnectionError: connect ECONNREFUSED **********:9200\n    at SniffingTransport._request (/app/node_modules/@elastic/transport/lib/Transport.js:595:31)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/node_modules/@elastic/transport/lib/Transport.js:627:32\n    at async SniffingTransport.request (/app/node_modules/@elastic/transport/lib/Transport.js:623:20)\n    at async Client.PingApi [as ping] (/app/node_modules/@elastic/elasticsearch/lib/api/api/ping.js:41:12)\n    at async DatabaseManager.connectElasticsearch (/app/src/config/database.js:73:7)\n    at async Promise.allSettled (index 2)\n    at async DatabaseManager.connectAll (/app/src/config/database.js:109:21)\n    at async ExLogServer.connectDatabases (/app/src/index.js:135:7)\n    at async ExLogServer.start (/app/src/index.js:146:7)","timestamp":"2025-06-02 13:52:08:528"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:13:47:1347","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:18:41:1841","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:20:46:2046","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:25:49:2549","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"code":11000,"errorLabelSet":{},"errorResponse":{"code":11000,"errmsg":"E11000 duplicate key error collection: exlog.logs index: logId_1 dup key: { logId: \"94feaade-5de3-44b7-958a-13ea30ad8c41\" }","index":0,"keyPattern":{"logId":1},"keyValue":{"logId":"94feaade-5de3-44b7-958a-13ea30ad8c41"}},"index":0,"keyPattern":{"logId":1},"keyValue":{"logId":"94feaade-5de3-44b7-958a-13ea30ad8c41"},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to process log 94feaade-5de3-44b7-958a-13ea30ad8c41: E11000 duplicate key error collection: exlog.logs index: logId_1 dup key: { logId: \"94feaade-5de3-44b7-958a-13ea30ad8c41\" }\u001b[39m","stack":"MongoServerError: E11000 duplicate key error collection: exlog.logs index: logId_1 dup key: { logId: \"94feaade-5de3-44b7-958a-13ea30ad8c41\" }\n    at InsertOneOperation.execute (/app/node_modules/mongodb/lib/operations/insert.js:51:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async tryOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:207:20)\n    at async executeOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:75:16)\n    at async Collection.insertOne (/app/node_modules/mongodb/lib/collection.js:157:16)","timestamp":"2025-06-02 14:30:15:3015"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:35:06:356","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"code":11000,"errorLabelSet":{},"errorResponse":{"code":11000,"errmsg":"E11000 duplicate key error collection: exlog.logs index: logId_1 dup key: { logId: \"94feaade-5de3-44b7-958a-13ea30ad8c41\" }","index":0,"keyPattern":{"logId":1},"keyValue":{"logId":"94feaade-5de3-44b7-958a-13ea30ad8c41"}},"index":0,"keyPattern":{"logId":1},"keyValue":{"logId":"94feaade-5de3-44b7-958a-13ea30ad8c41"},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to process log 94feaade-5de3-44b7-958a-13ea30ad8c41: E11000 duplicate key error collection: exlog.logs index: logId_1 dup key: { logId: \"94feaade-5de3-44b7-958a-13ea30ad8c41\" }\u001b[39m","stack":"MongoServerError: E11000 duplicate key error collection: exlog.logs index: logId_1 dup key: { logId: \"94feaade-5de3-44b7-958a-13ea30ad8c41\" }\n    at InsertOneOperation.execute (/app/node_modules/mongodb/lib/operations/insert.js:51:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async tryOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:207:20)\n    at async executeOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:75:16)\n    at async Collection.insertOne (/app/node_modules/mongodb/lib/collection.js:157:16)","timestamp":"2025-06-02 14:36:49:3649"}
{"code":11000,"errorLabelSet":{},"errorResponse":{"code":11000,"errmsg":"E11000 duplicate key error collection: exlog.logs index: logId_1 dup key: { logId: \"94feaade-5de3-44b7-958a-13ea30ad8c41\" }","index":0,"keyPattern":{"logId":1},"keyValue":{"logId":"94feaade-5de3-44b7-958a-13ea30ad8c41"}},"index":0,"keyPattern":{"logId":1},"keyValue":{"logId":"94feaade-5de3-44b7-958a-13ea30ad8c41"},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to process log 94feaade-5de3-44b7-958a-13ea30ad8c41: E11000 duplicate key error collection: exlog.logs index: logId_1 dup key: { logId: \"94feaade-5de3-44b7-958a-13ea30ad8c41\" }\u001b[39m","stack":"MongoServerError: E11000 duplicate key error collection: exlog.logs index: logId_1 dup key: { logId: \"94feaade-5de3-44b7-958a-13ea30ad8c41\" }\n    at InsertOneOperation.execute (/app/node_modules/mongodb/lib/operations/insert.js:51:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async tryOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:207:20)\n    at async executeOperation (/app/node_modules/mongodb/lib/operations/execute_operation.js:75:16)\n    at async Collection.insertOne (/app/node_modules/mongodb/lib/collection.js:157:16)","timestamp":"2025-06-02 14:37:09:379"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:38:21:3821","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:45:47:4547","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:45:51:4551","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:45:56:4556","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:45:56:4556","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:46:03:463","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:46:03:463","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:46:07:467","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:46:07:467","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:46:07:467","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:54:20:5420","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:54:25:5425","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:54:31:5431","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:54:32:5432","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:54:32:5432","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:54:32:5432","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:57:04:574","url":"/api/v1/logs?page=1&limit=50&search=&source=&logLevel=critical&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:57:11:5711","url":"/api/v1/logs?page=1&limit=50&search=&source=System&logLevel=&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:57:16:5716","url":"/api/v1/logs?page=1&limit=50&search=system&source=System&logLevel=&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:58:49:5849","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:59:29:5929","url":"/api/v1/logs?page=2&limit=50&source=&logLevel=&host=&search=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:59:33:5933","url":"/api/v1/logs?page=1&limit=50&search=&source=&logLevel=critical&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:59:43:5943","url":"/api/v1/logs?page=1&limit=50&search=&source=System&logLevel=&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:59:51:5951","url":"/api/v1/logs?page=1&limit=50&search=fail&source=&logLevel=&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 14:59:56:5956","url":"/api/v1/logs?page=1&limit=50&source=&logLevel=&host=&search=fail","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:00:05:05","url":"/api/v1/logs?page=1&limit=50&source=&logLevel=&host=&search=fail","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:00:14:014","url":"/api/v1/logs?page=2&limit=50&source=&logLevel=&host=&search=fail","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:00:31:031","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:00:44:044","url":"/api/v1/logs?page=1&limit=50&source=&logLevel=&host=&search=fail","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:00:47:047","url":"/api/v1/logs?page=2&limit=50&source=&logLevel=&host=&search=fail","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:00:48:048","url":"/api/v1/logs?page=2&limit=50&source=&logLevel=&host=&search=fail","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:00:52:052","url":"/api/v1/logs?page=2&limit=50&source=&logLevel=&host=&search=fail","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:01:07:17","url":"/api/v1/logs?page=1&limit=50&source=&logLevel=&host=&search=fail","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:01:15:115","url":"/api/v1/logs?page=2&limit=50&source=&logLevel=&host=&search=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:07:02:72","url":"/api/v1/logs?page=1&limit=50&search=&source=&logLevel=critical&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:07:08:78","url":"/api/v1/logs?page=1&limit=50&search=&source=System&logLevel=&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:08:18:818","url":"/api/v1/logs?page=1&limit=50&search=&source=&logLevel=info&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:08:19:819","url":"/api/v1/logs?page=1&limit=50&search=&source=&logLevel=info&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:08:19:819","url":"/api/v1/logs?page=1&limit=50&search=&source=&logLevel=info&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:08:19:819","url":"/api/v1/logs?page=1&limit=50&search=&source=&logLevel=info&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:08:19:819","url":"/api/v1/logs?page=1&limit=50&search=&source=&logLevel=info&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:08:19:819","url":"/api/v1/logs?page=1&limit=50&search=&source=&logLevel=info&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:08:20:820","url":"/api/v1/logs?page=1&limit=50&search=&source=&logLevel=info&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:08:20:820","url":"/api/v1/logs?page=1&limit=50&search=&source=&logLevel=info&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:08:20:820","url":"/api/v1/logs?page=1&limit=50&search=&source=&logLevel=info&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"GET","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:408:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:08:20:820","url":"/api/v1/logs?page=1&limit=50&search=&source=&logLevel=info&host=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"683a1b564430c4061ad861e0"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to Elasticsearch: connect ECONNREFUSED **********:9200\u001b[39m","meta":{"headers":{},"meta":{"aborted":false,"attempts":3,"connection":{"_openRequests":0,"deadCount":0,"headers":{"user-agent":"elasticsearch-js/8.18.2 (linux **********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"id":"http://elasticsearch:9200/","maxEventListeners":100,"pool":{"_events":{},"_eventsCount":1},"resurrectTimeout":0,"timeout":30000,"tls":null,"url":"http://elasticsearch:9200/","weight":1000},"context":null,"name":"elasticsearch-js","request":{"id":1,"options":{},"params":{"headers":{"accept":"application/vnd.elasticsearch+json; compatible-with=8,text/plain","user-agent":"elasticsearch-js/8.18.2 (linux **********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"method":"HEAD","path":"/","querystring":""}}},"statusCode":0,"warnings":null},"name":"ConnectionError","options":{"redaction":{"additionalKeys":[],"type":"replace"}},"stack":"ConnectionError: connect ECONNREFUSED **********:9200\n    at SniffingTransport._request (/app/node_modules/@elastic/transport/lib/Transport.js:595:31)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/node_modules/@elastic/transport/lib/Transport.js:627:32\n    at async SniffingTransport.request (/app/node_modules/@elastic/transport/lib/Transport.js:623:20)\n    at async Client.PingApi [as ping] (/app/node_modules/@elastic/elasticsearch/lib/api/api/ping.js:41:12)\n    at async DatabaseManager.connectElasticsearch (/app/src/config/database.js:73:7)\n    at async Promise.allSettled (index 2)\n    at async DatabaseManager.connectAll (/app/src/config/database.js:109:21)\n    at async ExLogServer.connectDatabases (/app/src/index.js:135:7)\n    at async ExLogServer.start (/app/src/index.js:146:7)","timestamp":"2025-06-02 15:18:22:1822"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:20:44:2044","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:23:14:2314","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"ip":"::ffff:**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:200:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-02 15:34:15:3415","url":"/api/v1/logs","userAgent":"python-requests/2.31.0","userId":"683a1b564430c4061ad861e0"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to Elasticsearch: connect ECONNREFUSED **********:9200\u001b[39m","meta":{"headers":{},"meta":{"aborted":false,"attempts":3,"connection":{"_openRequests":0,"deadCount":0,"headers":{"user-agent":"elasticsearch-js/8.18.2 (linux **********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"id":"http://elasticsearch:9200/","maxEventListeners":100,"pool":{"_events":{},"_eventsCount":1},"resurrectTimeout":0,"timeout":30000,"tls":null,"url":"http://elasticsearch:9200/","weight":1000},"context":null,"name":"elasticsearch-js","request":{"id":1,"options":{},"params":{"headers":{"accept":"application/vnd.elasticsearch+json; compatible-with=8,text/plain","user-agent":"elasticsearch-js/8.18.2 (linux **********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"method":"HEAD","path":"/","querystring":""}}},"statusCode":0,"warnings":null},"name":"ConnectionError","options":{"redaction":{"additionalKeys":[],"type":"replace"}},"stack":"ConnectionError: connect ECONNREFUSED **********:9200\n    at SniffingTransport._request (/app/node_modules/@elastic/transport/lib/Transport.js:595:31)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/node_modules/@elastic/transport/lib/Transport.js:627:32\n    at async SniffingTransport.request (/app/node_modules/@elastic/transport/lib/Transport.js:623:20)\n    at async Client.PingApi [as ping] (/app/node_modules/@elastic/elasticsearch/lib/api/api/ping.js:41:12)\n    at async DatabaseManager.connectElasticsearch (/app/src/config/database.js:73:7)\n    at async Promise.allSettled (index 2)\n    at async DatabaseManager.connectAll (/app/src/config/database.js:109:21)\n    at async ExLogServer.connectDatabases (/app/src/index.js:135:7)\n    at async ExLogServer.start (/app/src/index.js:146:7)","timestamp":"2025-06-02 15:36:54:3654"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to Elasticsearch: connect ECONNREFUSED **********:9200\u001b[39m","meta":{"headers":{},"meta":{"aborted":false,"attempts":3,"connection":{"_openRequests":0,"deadCount":0,"headers":{"user-agent":"elasticsearch-js/8.18.2 (linux **********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"id":"http://elasticsearch:9200/","maxEventListeners":100,"pool":{"_events":{},"_eventsCount":1},"resurrectTimeout":0,"timeout":30000,"tls":null,"url":"http://elasticsearch:9200/","weight":1000},"context":null,"name":"elasticsearch-js","request":{"id":1,"options":{},"params":{"headers":{"accept":"application/vnd.elasticsearch+json; compatible-with=8,text/plain","user-agent":"elasticsearch-js/8.18.2 (linux **********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.6)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.6,un=18.20.8"},"method":"HEAD","path":"/","querystring":""}}},"statusCode":0,"warnings":null},"name":"ConnectionError","options":{"redaction":{"additionalKeys":[],"type":"replace"}},"stack":"ConnectionError: connect ECONNREFUSED **********:9200\n    at SniffingTransport._request (/app/node_modules/@elastic/transport/lib/Transport.js:595:31)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/node_modules/@elastic/transport/lib/Transport.js:627:32\n    at async SniffingTransport.request (/app/node_modules/@elastic/transport/lib/Transport.js:623:20)\n    at async Client.PingApi [as ping] (/app/node_modules/@elastic/elasticsearch/lib/api/api/ping.js:41:12)\n    at async DatabaseManager.connectElasticsearch (/app/src/config/database.js:73:7)\n    at async Promise.allSettled (index 2)\n    at async DatabaseManager.connectAll (/app/src/config/database.js:109:21)\n    at async ExLogServer.connectDatabases (/app/src/index.js:135:7)\n    at async ExLogServer.start (/app/src/index.js:146:7)","timestamp":"2025-06-02 16:03:53:353"}
